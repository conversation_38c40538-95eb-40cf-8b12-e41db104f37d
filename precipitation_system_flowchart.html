
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>降水查询系统全流程脑图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 100%;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .description {
            background-color: #e8f4fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 5px solid #3498db;
        }
        .mermaid {
            text-align: center;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .legend {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        .legend h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .legend-item {
            display: inline-block;
            margin: 5px 15px 5px 0;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.9em;
        }
        .legend-start-end { background-color: #e1f5fe; border: 2px solid #01579b; }
        .legend-process { background-color: #f3e5f5; border: 2px solid #4a148c; }
        .legend-decision { background-color: #fff3e0; border: 2px solid #e65100; }
        .legend-user { background-color: #e8f5e8; border: 2px solid #2e7d32; }
        .legend-data { background-color: #fce4ec; border: 2px solid #c2185b; }
        .legend-error { background-color: #ffebee; border: 2px solid #d32f2f; }
        .legend-success { background-color: #e0f2f1; border: 2px solid #00695c; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌧️ 降水查询系统全流程脑图</h1>
        
        <div class="description">
            <h3>📋 系统概述</h3>
            <p>本流程图展示了基于Streamlit的降水查询系统的完整工作流程，包括：</p>
            <ul>
                <li><strong>系统初始化：</strong>模块导入、页面配置、样式设置</li>
                <li><strong>用户交互：</strong>查询条件设置、数据查询触发</li>
                <li><strong>数据处理：</strong>API调用、数据清洗、分区处理</li>
                <li><strong>数据展示：</strong>统计卡片、表格视图、地图视图</li>
                <li><strong>数据编辑：</strong>可编辑表格、数据保存、验证</li>
                <li><strong>报告生成：</strong>PDF生成、地图缓存、下载功能</li>
            </ul>
            <p><strong>生成时间：</strong>2025-08-03 20:16:57</p>
        </div>
        
        <div class="mermaid">
flowchart TD
    A[🚀 系统启动] --> B[📦 导入模块和库]
    B --> C[⚙️ 页面配置设置]
    C --> D[🎨 CSS样式加载]
    D --> E[📊 Session State初始化]
    
    E --> F[🏠 主页面渲染]
    F --> G[📋 侧边栏查询条件]
    
    G --> H{用户是否点击查询?}
    H -->|否| I[⏳ 等待用户输入]
    I --> H
    
    H -->|是| J[📅 获取查询参数]
    J --> K[🔄 重置PDF状态]
    K --> L[🌐 调用Station API]
    
    L --> M{API调用成功?}
    M -->|否| N[❌ 显示错误信息]
    N --> I
    
    M -->|是| O[📊 获取原始数据]
    O --> P[🧹 数据清洗和类型转换]
    P --> Q[✅ 数据验证]
    Q --> R[📍 数据分区处理]
    
    R --> S[🏘️ 平谷区数据]
    R --> T[🌍 其他区域数据]
    
    S --> U[📈 计算统计指标]
    T --> U
    U --> V[💳 显示统计卡片]
    
    V --> W[📋 表格视图渲染]
    W --> X[🗺️ 地图视图准备]
    
    X --> Y{用户选择视图类型}
    Y -->|表格视图| Z[📊 可编辑数据表格]
    Y -->|地图视图| AA[🗺️ 降水分布地图]
    
    Z --> BB{用户是否编辑数据?}
    BB -->|否| CC[👀 数据浏览模式]
    BB -->|是| DD[✏️ 数据编辑模式]
    
    DD --> EE{用户点击保存?}
    EE -->|否| DD
    EE -->|是| FF[🔄 更新原始数据]
    FF --> GG[✅ 数据验证和类型转换]
    GG --> HH[🔄 重新生成显示数据]
    HH --> II[✅ 显示保存成功]
    II --> JJ[🔄 页面重新渲染]
    
    CC --> KK{用户点击生成PDF?}
    JJ --> KK
    AA --> KK
    
    KK -->|否| LL[⏳ 等待用户操作]
    LL --> Y
    
    KK -->|是| MM[📋 数据完整性检查]
    MM --> NN{数据是否完整?}
    NN -->|否| OO[⚠️ 显示警告信息]
    OO --> LL
    
    NN -->|是| PP[🗺️ 地图缓存检查]
    PP --> QQ{地图缓存存在?}
    QQ -->|是| RR[✅ 使用缓存地图]
    QQ -->|否| SS[🎨 生成新地图]
    
    SS --> TT{地图生成成功?}
    TT -->|否| UU[⚠️ 无地图模式]
    TT -->|是| VV[💾 缓存地图数据]
    VV --> RR
    
    RR --> WW[📊 准备PDF数据]
    UU --> WW
    WW --> XX[🔍 获取最大小时雨量]
    XX --> YY[📄 调用PDF生成模块]
    
    YY --> ZZ{PDF生成成功?}
    ZZ -->|否| AAA[❌ 显示生成失败]
    ZZ -->|是| BBB[✅ PDF生成完成]
    
    BBB --> CCC[📥 提供下载链接]
    CCC --> DDD[👁️ PDF预览功能]
    
    AAA --> LL
    DDD --> LL
    
    %% 样式定义
    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000
    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000
    classDef userAction fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    classDef dataProcess fill:#fce4ec,stroke:#c2185b,stroke-width:2px,color:#000
    classDef error fill:#ffebee,stroke:#d32f2f,stroke-width:2px,color:#000
    classDef success fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000
    
    %% 应用样式
    class A,LL startEnd
    class B,C,D,E,F,G,J,K,L,O,P,Q,R,S,T,U,V,W,X,FF,GG,HH,MM,PP,WW,XX,YY process
    class H,M,Y,BB,EE,KK,NN,QQ,TT,ZZ decision
    class I,CC,DD userAction
    class Z,AA,RR,SS,VV dataProcess
    class N,AAA,OO,UU error
    class II,BBB,CCC,DDD success
        </div>
        
        <div class="legend">
            <h3>🎨 图例说明</h3>
            <div class="legend-item legend-start-end">开始/等待状态</div>
            <div class="legend-item legend-process">系统处理</div>
            <div class="legend-item legend-decision">决策判断</div>
            <div class="legend-item legend-user">用户操作</div>
            <div class="legend-item legend-data">数据处理</div>
            <div class="legend-item legend-error">错误处理</div>
            <div class="legend-item legend-success">成功状态</div>
        </div>
    </div>
    
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });
    </script>
</body>
</html>
