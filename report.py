from reportlab.lib import colors
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import mm, cm
from reportlab.platypus import BaseDocTemplate, Paragraph, Spacer, Table, TableStyle, Image, PageBreak, KeepTogether, HRFlowable
from reportlab.platypus.flowables import HRFlowable
from reportlab.platypus import Frame, PageTemplate, NextPageTemplate
from reportlab.graphics.shapes import Drawing, Rect, String
from reportlab.graphics.charts.linecharts import HorizontalLine<PERSON>hart
from reportlab.graphics.charts.barcharts import VerticalBar<PERSON>hart
from reportlab.graphics.charts.piecharts import Pie
from reportlab.graphics.widgets.markers import makeMarker
import matplotlib.pyplot as plt
import numpy as np
import io
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os
from datetime import datetime
import pandas as pd
import math # Import math module for ceil

# --- Setup ---
try:
    # 注册思源黑体（先只注册普通体，确保中文显示正常）
    pdfmetrics.registerFont(TTFont('SourceHanSans', './SourceHanSansCN-Bold.ttf'))
    
    DEFAULT_FONT = 'SourceHanSans'
except Exception:
    print("Warning: Source Han Sans font not found. Using default Helvetica.")
    DEFAULT_FONT = 'Helvetica'
    # 注册Helvetica字体族
    from reportlab.pdfbase.pdfmetrics import registerFontFamily
    registerFontFamily('Helvetica', normal='Helvetica', bold='Helvetica-Bold', italic='Helvetica-Oblique', boldItalic='Helvetica-BoldOblique')

# Define sky blue color scheme with different saturations
IMG_HEADER_BG_COLOR = colors.HexColor('#87CEEB') # Sky blue for headers
IMG_AVG_ROW_BG_COLOR = colors.HexColor('#E0F6FF') # Very light sky blue for average rows  
IMG_HEADER_TEXT_COLOR = colors.HexColor('#1e3a8a') # Dark blue for header text
IMG_DATA_TEXT_COLOR = colors.HexColor('#1f2937') # Dark gray for better readability
IMG_GRID_COLOR = colors.HexColor('#bfdbfe') # Light blue for grid lines
IMG_BOX_COLOR = colors.HexColor('#3b82f6') # Medium blue for borders
IMG_SEPARATOR_COLOR = colors.HexColor('#6b7280') # Gray for separators

# Use new color names consistently
HEADER_BG_COLOR = IMG_HEADER_BG_COLOR
HEADER_TEXT_COLOR = IMG_HEADER_TEXT_COLOR
AVG_ROW_BG_COLOR = IMG_AVG_ROW_BG_COLOR
DATA_ROW_BG_COLOR = colors.HexColor('#E8F4FD') # Light sky blue for data rows
GRID_COLOR = IMG_GRID_COLOR
SEPARATOR_LINE_COLOR = IMG_SEPARATOR_COLOR
TITLE_COLOR = colors.HexColor('#1F2937') # Keep title distinct
SUBTITLE_COLOR = colors.HexColor('#4B5563') # Keep subtitle distinct
ERROR_TEXT_COLOR = colors.red # Added for error messages

# Global styles dictionary
REPORT_STYLES = None

def create_styles():
    global REPORT_STYLES
    styles = getSampleStyleSheet()
    
    # Title style - Compact for single page layout
    styles.add(ParagraphStyle(name='CustomTitle', fontName=DEFAULT_FONT, fontSize=18, leading=22, alignment=1, spaceAfter=3*mm, textColor=TITLE_COLOR))
    # Subtitle style - Compact spacing
    styles.add(ParagraphStyle(name='CustomSubtitle', fontName=DEFAULT_FONT, fontSize=12, leading=16, alignment=0, spaceBefore=1*mm, spaceAfter=2*mm, textColor=SUBTITLE_COLOR))
    # Section heading style - Reduced spaceBefore/After
    styles.add(ParagraphStyle(name='CustomHeading2', fontName=DEFAULT_FONT, fontSize=14, leading=18, spaceBefore=4*mm, spaceAfter=2*mm, textColor=TITLE_COLOR, alignment=0))
    # Body text style - Slightly reduced space
    styles.add(ParagraphStyle(name='CustomBodyText', fontName=DEFAULT_FONT, fontSize=10, leading=14, spaceBefore=3*mm, spaceAfter=3*mm, alignment=0))
    # Error text style
    styles.add(ParagraphStyle(name='ErrorText', parent=styles['CustomBodyText'], textColor=ERROR_TEXT_COLOR))

    # --- Styles for table elements (通过增大字体和颜色实现强调效果) ---
    # Style for main table headers - 表头使用更大字体和黑色
    styles.add(ParagraphStyle(name='TableHeaderStyle', parent=styles['Normal'], fontName=DEFAULT_FONT, fontSize=11, textColor=IMG_DATA_TEXT_COLOR, alignment=1))
    # Style for the Average row text - 平均行使用黑色文字
    styles.add(ParagraphStyle(name='AvgRowStyle', parent=styles['Normal'], fontName=DEFAULT_FONT, fontSize=10, textColor=IMG_DATA_TEXT_COLOR, alignment=1))  # 改为居中对齐
    # Style for township names - 乡镇名使用黑色文字
    styles.add(ParagraphStyle(name='TownshipNameStyle', parent=styles['Normal'], fontName=DEFAULT_FONT, fontSize=10, textColor=IMG_DATA_TEXT_COLOR, alignment=1))
    # Style for station name rows - 站点名使用标准大小，居中对齐
    styles.add(ParagraphStyle(name='StationRowStyle', parent=styles['Normal'], fontName=DEFAULT_FONT, fontSize=10, textColor=IMG_DATA_TEXT_COLOR, alignment=1))
    # Style for numeric data cells - 数字使用标准大小
    styles.add(ParagraphStyle(name='NumberStyle', parent=styles['Normal'], fontName=DEFAULT_FONT, fontSize=10, textColor=IMG_DATA_TEXT_COLOR, alignment=1))
    # Style for BOLD numeric data cells - 重要数值使用更大字体和红色
    styles.add(ParagraphStyle(name='BoldNumberStyle', parent=styles['Normal'], fontName=DEFAULT_FONT, fontSize=12, textColor=colors.HexColor('#dc2626'), alignment=1))
    
    # 降水量级别颜色样式 - 使用标准颜色
    styles.add(ParagraphStyle(name='BlueNumberStyle', parent=styles['Normal'], fontName=DEFAULT_FONT, fontSize=10, textColor=colors.HexColor('#0000FF'), alignment=1))  # 标准蓝色
    styles.add(ParagraphStyle(name='YellowNumberStyle', parent=styles['Normal'], fontName=DEFAULT_FONT, fontSize=10, textColor=colors.HexColor('#e6c100'), alignment=1))  # 亮金色
    styles.add(ParagraphStyle(name='OrangeNumberStyle', parent=styles['Normal'], fontName=DEFAULT_FONT, fontSize=10, textColor=colors.HexColor('#f56000'), alignment=1))  # 标准橙色
    styles.add(ParagraphStyle(name='RedNumberStyle', parent=styles['Normal'], fontName=DEFAULT_FONT, fontSize=10, textColor=colors.HexColor('#FF0000'), alignment=1))  # 标准红色
    
    # 概要表格中重要数值的样式（红色加大字号）
    styles.add(ParagraphStyle(name='ImportantValueStyle', parent=styles['CustomBodyText'], fontName=DEFAULT_FONT, fontSize=12, textColor=colors.HexColor('#dc2626')))

    REPORT_STYLES = styles
    return styles

def get_rainfall_color_style(rainfall_value, is_hourly=False):
    """根据降水量级别返回对应的颜色样式名称"""
    global REPORT_STYLES
    if not REPORT_STYLES or pd.isna(rainfall_value):
        return REPORT_STYLES['NumberStyle'] if REPORT_STYLES else None
    
    if is_hourly:
        # 近1小时雨量：>30蓝色，>50黄色，>70橙色，>100红色
        if rainfall_value >= 100:
            return REPORT_STYLES['RedNumberStyle']
        elif rainfall_value >= 70:
            return REPORT_STYLES['OrangeNumberStyle']
        elif rainfall_value >= 50:
            return REPORT_STYLES['YellowNumberStyle']
        elif rainfall_value >= 30:
            return REPORT_STYLES['BlueNumberStyle']
        else:
            return REPORT_STYLES['NumberStyle']
    else:
        # 总降雨量：>70蓝色，>100黄色，>150橙色，>200红色
        if rainfall_value >= 200:
            return REPORT_STYLES['RedNumberStyle']
        elif rainfall_value >= 150:
            return REPORT_STYLES['OrangeNumberStyle']
        elif rainfall_value >= 100:
            return REPORT_STYLES['YellowNumberStyle']
        elif rainfall_value >= 70:
            return REPORT_STYLES['BlueNumberStyle']
        else:
            return REPORT_STYLES['NumberStyle']

def header_footer(canvas, doc):
    # 保存状态
    canvas.saveState()
    
    # 页眉 - 减小占用空间
    canvas.setFont(DEFAULT_FONT, 7)
    canvas.setFillColor(SUBTITLE_COLOR)
    canvas.drawString(15 * mm, A4[1] - 8 * mm, f"生成: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
    
    # 删除页脚内容以节省空间
    
    # 恢复状态
    canvas.restoreState()

def create_top_hourly_stations_table(pg_data):
    """Creates the top stations summary table with updated styling."""
    global REPORT_STYLES
    if not REPORT_STYLES: return Spacer(1, 1)

    # Simplified data preparation - use top 5 from input (already filtered)
    top_5_total = pg_data.nlargest(5, '总降雨量')
    top_5_hourly = pg_data.nlargest(5, '近一小时雨量')

    # Calculate overall averages for Pinggu
    avg_total = pg_data['总降雨量'].mean()
    avg_hourly = pg_data['近一小时雨量'].mean()

    header = [
        Paragraph("排名", REPORT_STYLES['TableHeaderStyle']),
        Paragraph("总降雨量站点 (mm)", REPORT_STYLES['TableHeaderStyle']),
        Paragraph("近1h雨量站点 (mm)", REPORT_STYLES['TableHeaderStyle'])
    ]
    avg_row_content = [
        Paragraph("平均", REPORT_STYLES['AvgRowStyle']),
        Paragraph(f"{avg_total:.1f}" if not pd.isna(avg_total) else '-', REPORT_STYLES['AvgRowStyle']),
        Paragraph(f"{avg_hourly:.1f}" if not pd.isna(avg_hourly) else '-', REPORT_STYLES['AvgRowStyle'])
    ]

    table_data = [header, avg_row_content]

    for i in range(min(5, len(pg_data))):
        rank_str = f"{i+1}"
        # Handle cases where there might be fewer than 5 rows
        total_station_str = "-"
        hourly_station_str = "-"
        if i < len(top_5_total):
            row_total = top_5_total.iloc[i]
            total_station_str = f"{row_total['站名']} {row_total['总降雨量']:.1f}"
        if i < len(top_5_hourly):
            row_hourly = top_5_hourly.iloc[i]
            hourly_station_str = f"{row_hourly['站名']} {row_hourly['近一小时雨量']:.1f}"

        table_data.append([
            Paragraph(rank_str, REPORT_STYLES['NumberStyle']),
            Paragraph(total_station_str, REPORT_STYLES['StationRowStyle']),
            Paragraph(hourly_station_str, REPORT_STYLES['StationRowStyle'])
        ])

    # Adjust column widths
    table_width = A4[0] - 40 * mm # Available width assuming 15mm margins
    col_widths = [table_width * 0.15, table_width * 0.425, table_width * 0.425]

    table = Table(table_data, colWidths=col_widths)

    # Apply new styles (Reduced padding)
    style = TableStyle([
        # Header row (row 0)
        ('BACKGROUND', (0, 0), (-1, 0), HEADER_BG_COLOR),
        ('LINEBELOW', (0, 0), (-1, 0), 1, IMG_BOX_COLOR),
        ('VALIGN', (0, 0), (-1, 0), 'MIDDLE'),

        # Average row (row 1)
        ('BACKGROUND', (0, 1), (-1, 1), AVG_ROW_BG_COLOR),
        ('LINEBELOW', (0, 1), (-1, 1), 0.5, GRID_COLOR),

        # Data rows (row 2 onwards)
        ('BACKGROUND', (0, 2), (-1, -1), colors.white),

        # Box and Grid
        ('BOX', (0, 0), (-1, -1), 1, IMG_BOX_COLOR),
        ('GRID', (0, 1), (-1, -1), 0.5, GRID_COLOR), # Grid only below header

        # Alignment (Paragraphs handle text alignment)
        ('VALIGN', (0, 1), (-1, -1), 'MIDDLE'),

        # Padding (Compact for single page layout)
        ('LEFTPADDING', (0, 0), (-1, -1), 3),
        ('RIGHTPADDING', (0, 0), (-1, -1), 3),
        ('TOPPADDING', (0, 0), (-1, -1), 2), # Reduced for single page layout
        ('BOTTOMPADDING', (0, 0), (-1, -1), 2), # Reduced for single page layout
    ])

    table.setStyle(style)
    return table

def process_pg_data(pg_data, max_stations=3):
    """Processes Pinggu township data with manual ordering, returns list of dicts including total_station_count."""
    if not isinstance(pg_data, pd.DataFrame) or pg_data.empty:
        return []
    required_cols = ['乡镇', '站名', '总降雨量', '近一小时雨量']
    if not all(col in pg_data.columns for col in required_cols):
        print(f"Error: Pinggu data missing one or more required columns: {required_cols}")
        return []

    processed_list = []
    pg_data = pg_data.copy()
    pg_data['总降雨量'] = pd.to_numeric(pg_data['总降雨量'], errors='coerce')
    pg_data['近一小时雨量'] = pd.to_numeric(pg_data['近一小时雨量'], errors='coerce')
    grouped = pg_data.groupby('乡镇', dropna=False)
    
    # 加载全部站点信息CSV文件以获取每个乡镇的完整站点列表
    try:
        all_stations_df = pd.read_csv('全部站点信息.csv')
        # 过滤出平谷区的所有站点
        pg_all_stations = all_stations_df[all_stations_df['区县'] == '平谷'].copy()
        pg_all_stations_grouped = pg_all_stations.groupby('乡镇', dropna=False)
    except Exception as e:
        print(f"Warning: Failed to load 全部站点信息.csv: {e}")
        pg_all_stations_grouped = None

    # 人工指定的乡镇顺序和站点数量
    # 第一组：优先乡镇（2个站位）
    first_group = ['平谷镇', '滨河街道', '兴谷街道']
    # 第二组：2个站位的乡镇
    second_group = ['夏各庄镇', '马昌营镇', '熊儿寨乡']
    # 第三组：3个站位的乡镇
    third_group = ['东高村镇', '刘家店镇', '南独乐河镇', '大华山镇', '山东庄镇', '峪口镇', 
                   '王辛庄镇', '金海湖镇', '镇罗营镇', '马坊镇', '黄松峪乡', '大兴庄镇']
    

    

    
    # 不再使用TOP5逻辑，改为按降水量级别显示颜色

    # 按顺序处理各组乡镇，用不同的group_id标记，按降水量级别设置颜色
    def process_group_with_levels(township_list, station_slots, is_priority=False, group_id=1):
        for township_name in township_list:
            if township_name in grouped.groups:
                group_df = grouped.get_group(township_name)
            else:
                # 如果没有此乡镇的数据，创建一个空数据记录
                group_df = pd.DataFrame(columns=pg_data.columns)
            
            # 获取该乡镇在全部站点信息CSV中的总站点数
            if pg_all_stations_grouped is not None and township_name in pg_all_stations_grouped.groups:
                all_stations_in_township = pg_all_stations_grouped.get_group(township_name)
                total_station_count = len(all_stations_in_township)
                all_station_names = all_stations_in_township['站名'].tolist()
            else:
                # 如果CSV中没有这个乡镇的信息，回退到原有数据的站点数
                total_station_count = len(group_df) if not group_df.empty else 0
                all_station_names = group_df['站名'].tolist() if not group_df.empty else []
                
            valid_group = group_df.dropna(subset=['总降雨量', '近一小时雨量'])
            
            if valid_group.empty:
                avg_total, avg_hourly = float('nan'), float('nan')
                station_list = []
            else:
                avg_total = valid_group['总降雨量'].mean()
                avg_hourly = valid_group['近一小时雨量'].mean()
                stations_sorted = valid_group.sort_values('总降雨量', ascending=False)
                
                # 取指定数量的站点
                stations_to_show = stations_sorted.head(station_slots)
                station_list = []
                for _, row in stations_to_show.iterrows():
                    station_name = row['站名']
                    station_list.append({
                        'name': station_name, 
                        'total_rain': row['总降雨量'], 
                        'hourly_rain': row['近一小时雨量']
                    })
            
            # 如果有效数据站点不足指定数量，从全部站点中选择其他站点补齐
            if len(station_list) < station_slots:
                # 获取已经显示的站点名称
                shown_stations = [s['name'] for s in station_list]
                # 从全部站点名称中排除已显示的，选择剩余的站点来补齐
                remaining_stations = [name for name in all_station_names if name not in shown_stations]
                
                slots_needed = station_slots - len(station_list)
                for i in range(slots_needed):
                    if i < len(remaining_stations):
                        # 使用剩余站点的名称，数据显示为"-"
                        station_list.append({
                            'name': remaining_stations[i], 
                            'total_rain': float('nan'), 
                            'hourly_rain': float('nan')
                        })
                    else:
                        # 如果连剩余站点都不够，显示空行
                        station_list.append({
                            'name': '', 
                            'total_rain': float('nan'), 
                            'hourly_rain': float('nan')
                        })
            
            processed_list.append({
                'name': township_name, 'avg_total_rain': avg_total, 'avg_hourly_rain': avg_hourly,
                'total_station_count': total_station_count, 'stations': station_list,
                'is_priority': is_priority, 'group_id': group_id
            })
    
    process_group_with_levels(first_group, 2, is_priority=True, group_id=1)   # 第一组：2个站位，标记为优先
    process_group_with_levels(second_group, 2, is_priority=False, group_id=2) # 第二组：2个站位
    process_group_with_levels(third_group, 3, is_priority=False, group_id=3)  # 第三组：3个站位
    
    return processed_list

# --- CREATE SINGLE TOWNSHIP TABLE BLOCK (Copied from v3) ---
def create_township_table(township_data, col_widths):
    """Creates a table block for a single township's data."""
    global REPORT_STYLES
    if not REPORT_STYLES: return Spacer(1, 1)
    
    # 乡镇名竖向显示（每个字符用<br/>分隔）
    # 对于地区名（如"密云区"），如果名称过长，使用横向显示
    township_name = township_data['name']
    if len(township_name) > 4:  # 如果名称超过4个字符，使用横向显示
        township_name_vertical = township_name
    else:
        township_name_vertical = '<br/>'.join(list(township_name))
    
    # 平均值行显示站点数信息
    avg_name_with_count = f"平均({township_data['total_station_count']}站)"
    avg_total_str = f"{township_data['avg_total_rain']:.1f}" if not pd.isna(township_data['avg_total_rain']) else '-'
    avg_hourly_str = f"{township_data['avg_hourly_rain']:.1f}" if not pd.isna(township_data['avg_hourly_rain']) else '-'
    
    # 构建表格数据 - 新增第一列显示乡镇名
    table_data = []
    
    # 平均值行：乡镇名(竖向) | "平均(X站)" | 总雨量平均值 | 近1h雨量平均值
    avg_row = [
        Paragraph(township_name_vertical, REPORT_STYLES['TownshipNameStyle']), 
        Paragraph(avg_name_with_count, REPORT_STYLES['AvgRowStyle']), 
        Paragraph(avg_total_str, REPORT_STYLES['AvgRowStyle']), 
        Paragraph(avg_hourly_str, REPORT_STYLES['AvgRowStyle'])
    ]
    table_data.append(avg_row)
    
    # 站点数据行：乡镇名(合并) | 站点名 | 总雨量 | 近1h雨量
    for station in township_data['stations']:
        name_str = station['name'] if station['name'] else ""
        
        # 准备雨量数值字符串，NaN显示为"-"
        total_rain_val = f"{station['total_rain']:.1f}" if not pd.isna(station['total_rain']) else '-'
        hourly_rain_val = f"{station['hourly_rain']:.1f}" if not pd.isna(station['hourly_rain']) else '-'

        # 根据降水量级别选择颜色样式，对于NaN值使用默认样式
        if pd.isna(station['total_rain']):
            total_para_style = REPORT_STYLES['NumberStyle']
        else:
            total_para_style = get_rainfall_color_style(station['total_rain'], is_hourly=False)
            
        if pd.isna(station['hourly_rain']):
            hourly_para_style = REPORT_STYLES['NumberStyle']
        else:
            hourly_para_style = get_rainfall_color_style(station['hourly_rain'], is_hourly=True)
        
        station_row = [
            Paragraph("", REPORT_STYLES['StationRowStyle']),
            Paragraph(name_str, REPORT_STYLES['StationRowStyle']), 
            Paragraph(total_rain_val, total_para_style), # 应用选择的样式
            Paragraph(hourly_rain_val, hourly_para_style) # 应用选择的样式
        ]
        table_data.append(station_row)
    
    # 为了对齐，确保所有表格都有相同行数（平均行+3个站点行=4行）
    # 如果当前站点数少于3个，添加空行补齐
    target_total_rows = 4  # 1个平均行 + 3个站点行
    current_rows = len(table_data)
    while current_rows < target_total_rows:
        empty_row = [
            Paragraph("", REPORT_STYLES['StationRowStyle']),  # 空白，因为会被合并
            Paragraph("", REPORT_STYLES['StationRowStyle']), 
            Paragraph("", REPORT_STYLES['NumberStyle']), 
            Paragraph("", REPORT_STYLES['NumberStyle'])
        ]
        table_data.append(empty_row)
        current_rows += 1
    
    township_table = Table(table_data, colWidths=col_widths)
    
    # 计算合并行数（平均行 + 站点行）
    total_rows = len(table_data)
    
    # Apply style (Reduced padding) with unified borders
    table_style = TableStyle([
        # 背景色 - 乡镇名列使用与平均行相同的背景色
        ('BACKGROUND', (0, 0), (0, total_rows - 1), AVG_ROW_BG_COLOR),  # 第一列(乡镇名)背景色
        ('BACKGROUND', (1, 0), (-1, 0), AVG_ROW_BG_COLOR),              # 平均行其他列背景色
        ('LINEBELOW', (0, 0), (-1, 0), 0.5, GRID_COLOR),
        ('BACKGROUND', (1, 1), (-1, -1), colors.white),                # 站点数据行背景色改为白色
        
        # 合并第一列的所有行来显示乡镇名
        ('SPAN', (0, 0), (0, total_rows - 1)),
        
        # 对齐方式
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        
        # 第一列(乡镇名列)居中对齐
        ('ALIGN', (0, 0), (0, total_rows - 1), 'CENTER'),
        
        # 统一边框设置 - 每个乡镇表格都有完整的边框
        ('BOX', (0, 0), (-1, -1), 1, IMG_BOX_COLOR),  # 外围边框
        ('GRID', (1, 1), (-1, -1), 0.5, GRID_COLOR), # 内部数据行网格
        
        # Padding (Compact for single page layout)
        ('LEFTPADDING', (0, 0), (-1, -1), 3),
        ('RIGHTPADDING', (0, 0), (-1, -1), 3),
        ('TOPPADDING', (0, 0), (-1, -1), 2), # Reduced for single page layout
        ('BOTTOMPADDING', (0, 0), (-1, -1), 2), # Reduced for single page layout
    ])
    township_table.setStyle(table_style)
    return township_table

# --- Modified function: Create Three Column Township Layout ---
def create_three_column_township_layout(all_township_data, available_width):
    """Creates the header and the three-column table layout for townships, with first row showing priority townships."""
    global REPORT_STYLES
    if not REPORT_STYLES: return [] # Return empty list if styles fail

    num_columns = 3
    column_gap = 3 * mm
    column_width = (available_width - (num_columns - 1) * column_gap) / num_columns
    # 调整内部列宽：乡镇名列(竖向，更窄) | 站点名列 | 总雨量列 | 近1h雨量列
    inner_col_widths = [column_width * 0.12, column_width * 0.40, column_width * 0.24, column_width * 0.24]

    # --- Create Column Headers ---
    header_content = [
        # Use existing TableHeaderStyle from report.py
        Paragraph("乡镇", REPORT_STYLES['TableHeaderStyle']),
        Paragraph("站址", REPORT_STYLES['TableHeaderStyle']),
        Paragraph("总雨量", REPORT_STYLES['TableHeaderStyle']),
        Paragraph("近1h雨量", REPORT_STYLES['TableHeaderStyle'])
    ]
    header_inner_table = Table([header_content], colWidths=inner_col_widths)
    header_inner_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), AVG_ROW_BG_COLOR), # 与平均行使用相同背景色
        ('VALIGN', (0, 0), (-1, 0), 'MIDDLE'),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 2),
        ('TOPPADDING', (0, 0), (-1, 0), 2),
        # 添加表格线
        ('BOX', (0, 0), (-1, 0), 1, IMG_BOX_COLOR),  # 外围边框改为蓝色
        ('GRID', (0, 0), (-1, 0), 0.5, IMG_BOX_COLOR),  # 内部网格线改为蓝色
    ]))
    header_row_table = Table([[header_inner_table] * num_columns],
                           colWidths=[column_width] * num_columns)
    header_row_table.setStyle(TableStyle([
        ('LEFTPADDING', (0, 0), (-1, -1), 0),
        ('RIGHTPADDING', (0, 0), (num_columns - 2, 0), column_gap),
        ('TOPPADDING', (0, 0), (-1, -1), 0),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 1*mm),
        # 为整体header添加完整边框
        ('LINEABOVE', (0, 0), (-1, 0), 1, IMG_BOX_COLOR),  # 顶部边框
        ('LINEBEFORE', (0, 0), (0, 0), 1, IMG_BOX_COLOR), # 左边框
        ('LINEAFTER', (-1, 0), (-1, 0), 1, IMG_BOX_COLOR), # 右边框
        ('LINEBELOW', (0, 0), (-1, 0), 1, IMG_BOX_COLOR),  # 底部边框
        # 添加列间分隔线
        ('LINEAFTER', (0, 0), (num_columns - 2, 0), 0.5, GRID_COLOR),  # 列间分隔线
    ]))

    # --- 分离优先乡镇和其他乡镇，并按组别排序 ---
    priority_townships = [data for data in all_township_data if data.get('is_priority', False)]
    other_townships = [data for data in all_township_data if not data.get('is_priority', False)]
    
    # 按group_id排序其他乡镇，确保第二组(group_id=2)在第三组(group_id=3)前面
    other_townships.sort(key=lambda x: x.get('group_id', 999))
    
    # 确保有三个优先乡镇（应该是平谷镇、滨河街道、兴谷街道）
    while len(priority_townships) < 3:
        # 添加缺失的乡镇（空数据）
        missing_name = "缺失乡镇"
        priority_townships.append({
            'name': missing_name, 'avg_total_rain': float('nan'), 'avg_hourly_rain': float('nan'),
            'total_station_count': 0, 'stations': [
                {'name': '', 'total_rain': float('nan'), 'hourly_rain': float('nan')},
                {'name': '', 'total_rain': float('nan'), 'hourly_rain': float('nan')}
            ],
            'is_priority': True
        })
    
    # 创建第一行的三个乡镇表格
    first_row_tables = []
    for township_data in priority_townships[:3]:  # 只取前三个
        township_table = create_township_table(township_data, inner_col_widths)
        if isinstance(township_table, Table):
            first_row_tables.append(township_table)
        else:
            # 如果创建表格失败，添加空白表格
            print(f"Warning: Failed to create table for township {township_data.get('name', 'Unknown')}")
            first_row_tables.append(Spacer(column_width, 10*mm))
    
    # 确保第一行有3个表格
    while len(first_row_tables) < 3:
        first_row_tables.append(Spacer(column_width, 10*mm))
    
    # 创建第一行表格（固定显示优先乡镇）
    first_row_table = Table(
        [first_row_tables],
        colWidths=[column_width] * num_columns,
        style=TableStyle([
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (num_columns - 2, -1), column_gap),
            ('TOPPADDING', (0, 0), (-1, -1), 0),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 4*mm),  # 增加底部间距，与其他行保持一致
            # 添加左右边框，保持与概要表格的连续性
            ('LINEBEFORE', (0, 0), (0, -1), 1, IMG_BOX_COLOR), # 左边框
            ('LINEAFTER', (-1, 0), (-1, -1), 1, IMG_BOX_COLOR), # 右边框
        ])
    )
    
    # --- 处理其他乡镇 (按group_id顺序分配到三列) ---
    columns = [[] for _ in range(num_columns)]
    current_column = 0  # 当前分配的列索引

    # 按group_id顺序处理，为每个表格添加统一的间距
    for i, township_data in enumerate(other_townships):
        # 为这个乡镇创建表格
        township_table = create_township_table(township_data, inner_col_widths)

        if isinstance(township_table, Table):
            # 如果不是该列的第一个表格，添加上间距
            if columns[current_column]:
                # 通过 Spacer 添加统一的间距，替代不一致的分隔线
                columns[current_column].append(Spacer(1, 3*mm))

            # 将表格添加到当前列
            columns[current_column].append(township_table)
            
            # 轮换到下一列
            current_column = (current_column + 1) % num_columns
        else:
            print(f"Warning: Failed to create table for township {township_data.get('name', 'Unknown')}")

    # --- 创建其他乡镇的表格 ---
    if any(columns):  # 只有当有内容时才创建表格
        other_townships_table = Table(
            [columns],
            colWidths=[column_width] * num_columns,
            style=TableStyle([
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (num_columns - 2, -1), column_gap),
                ('TOPPADDING', (0, 0), (-1, -1), 0),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 0),
                # 添加边框，完成整个表格的封闭
                ('LINEBEFORE', (0, 0), (0, -1), 1, IMG_BOX_COLOR), # 左边框
                ('LINEAFTER', (-1, 0), (-1, -1), 1, IMG_BOX_COLOR), # 右边框
                ('LINEBELOW', (0, -1), (-1, -1), 1, IMG_BOX_COLOR), # 底部边框
            ])
        )
        return [header_row_table, first_row_table, other_townships_table]
    else:
        # 如果没有其他乡镇，需要为first_row_table添加底部边框
        first_row_table_style = first_row_table._cellStyles[0][0]
        first_row_table_style.add('LINEBELOW', (0, -1), (-1, -1), 1, IMG_BOX_COLOR)
        return [header_row_table, first_row_table]

# --- NEW FUNCTION: CREATE SINGLE DISTRICT TABLE BLOCK ---
def create_district_table(district_data, col_widths):
    """Creates a table block for a single district's data."""
    global REPORT_STYLES
    if not REPORT_STYLES: return Spacer(1, 1)
    # District name and total station count
    avg_name_str = f"{district_data['name']} ({district_data['total_station_count']}站)"
    avg_total_str = f"{district_data['avg_total_rain']:.1f}" if not pd.isna(district_data['avg_total_rain']) else '&nbsp;'
    avg_hourly_str = f"{district_data['avg_hourly_rain']:.1f}" if not pd.isna(district_data['avg_hourly_rain']) else '&nbsp;'
    avg_row = [Paragraph(avg_name_str, REPORT_STYLES['AvgRowStyle']), Paragraph(avg_total_str, REPORT_STYLES['NumberStyle']), Paragraph(avg_hourly_str, REPORT_STYLES['NumberStyle'])]
    table_data = [avg_row]
    # List all stations for the district
    for station in district_data['stations']:
        name_str = station['name']
        total_str = f"{station['total_rain']:.1f}" if not pd.isna(station['total_rain']) else '&nbsp;'
        hourly_str = f"{station['hourly_rain']:.1f}" if not pd.isna(station['hourly_rain']) else '&nbsp;'
        table_data.append([Paragraph(name_str, REPORT_STYLES['StationRowStyle']), Paragraph(total_str, REPORT_STYLES['NumberStyle']), Paragraph(hourly_str, REPORT_STYLES['NumberStyle'])])
    district_table = Table(table_data, colWidths=col_widths)
    # Apply the same style as the township table (Reduced padding)
    table_style = TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), AVG_ROW_BG_COLOR),
        ('LINEBELOW', (0, 0), (-1, 0), 0.5, GRID_COLOR),
        ('BACKGROUND', (0, 1), (-1, -1), colors.white),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        # Padding (Compact for single page layout)
        ('LEFTPADDING', (0, 0), (-1, -1), 3),
        ('RIGHTPADDING', (0, 0), (-1, -1), 3),
        ('TOPPADDING', (0, 0), (-1, -1), 2), # Reduced for single page layout
        ('BOTTOMPADDING', (0, 0), (-1, -1), 2), # Reduced for single page layout
    ])
    district_table.setStyle(table_style)
    return district_table

def create_summary_table(pg_data, query_start_time, query_end_time, available_width, max_intensity_data=None):
    """创建雨情快报概要表格"""
    global REPORT_STYLES
    if not REPORT_STYLES: 
        return Spacer(1, 1)
    
    # 如果没有传入降水强度数据，使用默认值
    if max_intensity_data is None:
        max_intensity_data = {
            'station_name': '无数据',
            'start_time': '--:--',
            'end_time': '--:--', 
            'intensity': 0.0
        }
    
    # 计算统计数据
    if not pg_data.empty:
        avg_rainfall = pg_data['总降雨量'].mean()
        max_rainfall_row = pg_data.loc[pg_data['总降雨量'].idxmax()]
        max_rainfall = max_rainfall_row['总降雨量']
        max_rainfall_station = max_rainfall_row['站名']
    else:
        avg_rainfall = 0
        max_rainfall = 0
        max_rainfall_station = "无数据"
    
    # 格式化时间
    time_format = '%Y-%m-%d %H:%M'
    time_str = f"{query_start_time.strftime(time_format)} 至 {query_end_time.strftime(time_format)}"
    
    # 使用与详细表格完全一致的总宽度计算
    # 详细表格的计算：column_width * num_columns，但不包括列间隙
    # 为了完全对齐，概要表格也使用同样的总宽度
    num_detail_columns = 3
    detail_column_gap = 3 * mm
    detail_column_width = (available_width - (num_detail_columns - 1) * detail_column_gap) / num_detail_columns
    detail_total_width = detail_column_width * num_detail_columns
    
    # 概要表格使用相同的实际总宽度
    col_widths = [detail_total_width * 0.5, detail_total_width * 0.5]  # 两列等宽
    
    # 重新构建表格数据，确保每行都有两列
    formatted_table_data = [
        # 第一行：标题（跨两列）
        [Paragraph("平谷区雨情快报（单位：毫米）", REPORT_STYLES['CustomTitle']), ""],
        
        # 第二行：降水时间（跨两列）
        [Paragraph(f"降水时间：{time_str}", REPORT_STYLES['CustomSubtitle']), ""],
        
        # 第三行：平均降水量和最大降水量（两列）
        [
            Paragraph(f"全区平均降水量：{avg_rainfall:.1f}", REPORT_STYLES['CustomBodyText']),
            Paragraph(f"最大降水量：<font color='#dc2626' size='12'>{max_rainfall:.1f}</font>（{max_rainfall_station}）", REPORT_STYLES['CustomBodyText'])
        ],
        
        # 第四行：平谷区最大降水强度（跨两列）
        [Paragraph(f"平谷区最大降水强度：<font color='#dc2626' size='12'>{max_intensity_data['intensity']:.1f}</font>（{max_intensity_data['station_name']} {max_intensity_data['start_time']}-{max_intensity_data['end_time']}）", REPORT_STYLES['CustomBodyText']), ""]
    ]
    
    # 创建表格，使用与详细表格完全一致的总宽度
    summary_table = Table(formatted_table_data, colWidths=col_widths)
    
    # 应用样式
    style = TableStyle([
        # 跨列设置
        ('SPAN', (0, 0), (1, 0)),  # 第一行标题跨两列
        ('SPAN', (0, 1), (1, 1)),  # 第二行时间跨两列  
        ('SPAN', (0, 3), (1, 3)),  # 第四行降水强度跨两列
        
        # 第一行（标题）样式 - 紧凑设计
        ('BACKGROUND', (0, 0), (-1, 0), HEADER_BG_COLOR),
        ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
        ('VALIGN', (0, 0), (-1, 0), 'MIDDLE'),
        ('FONTSIZE', (0, 0), (-1, 0), 14),  # 减小字体从16到14
        ('FONTNAME', (0, 0), (-1, 0), DEFAULT_FONT),
        ('TEXTCOLOR', (0, 0), (-1, 0), TITLE_COLOR),
        ('TOPPADDING', (0, 0), (-1, 0), 4),  # 减小padding从8到4
        ('BOTTOMPADDING', (0, 0), (-1, 0), 4),
        
        # 第二行（时间）样式 - 与其他行保持一致
        ('BACKGROUND', (0, 1), (-1, 1), DATA_ROW_BG_COLOR),  # 改为数据行背景色
        ('ALIGN', (0, 1), (-1, 1), 'CENTER'),
        ('VALIGN', (0, 1), (-1, 1), 'MIDDLE'),
        ('TOPPADDING', (0, 1), (-1, 1), 3),  # 减小padding
        ('BOTTOMPADDING', (0, 1), (-1, 1), 3),
        
        # 第三行（统计数据）样式 - 两列分别设置
        ('BACKGROUND', (0, 2), (-1, 2), DATA_ROW_BG_COLOR),
        ('ALIGN', (0, 2), (-1, 2), 'CENTER'),
        ('VALIGN', (0, 2), (-1, 2), 'MIDDLE'),
        ('TOPPADDING', (0, 2), (-1, 2), 3),  # 减小padding
        ('BOTTOMPADDING', (0, 2), (-1, 2), 3),
        
        # 第四行（降水强度）样式
        ('BACKGROUND', (0, 3), (-1, 3), AVG_ROW_BG_COLOR),
        ('ALIGN', (0, 3), (-1, 3), 'CENTER'),
        ('VALIGN', (0, 3), (-1, 3), 'MIDDLE'),
        ('TOPPADDING', (0, 3), (-1, 3), 3),  # 减小padding
        ('BOTTOMPADDING', (0, 3), (-1, 3), 3),
        
        # 整体边框 - 与详细表格完全一致的边框设置
        ('LINEABOVE', (0, 0), (-1, 0), 1, IMG_BOX_COLOR),  # 顶部边框
        ('LINEBEFORE', (0, 0), (0, -1), 1, IMG_BOX_COLOR), # 左边框
        ('LINEAFTER', (-1, 0), (-1, -1), 1, IMG_BOX_COLOR), # 右边框
        # 不设置底部边框，让它与下方表格连接
        
        # 第三行中间分隔线（只有第三行需要分隔两列）
        ('LINEAFTER', (0, 2), (0, 2), 0.5, GRID_COLOR),
        
        # 行间分隔线
        ('LINEBELOW', (0, 0), (-1, 2), 0.5, GRID_COLOR),
        
        # 左右内边距
        ('LEFTPADDING', (0, 0), (-1, -1), 6),
        ('RIGHTPADDING', (0, 0), (-1, -1), 6),
    ])
    
    summary_table.setStyle(style)
    return summary_table

def generate_pdf(data, query_start_time, query_end_time, map_image_buffer, output_filename="report_styled.pdf", max_intensity_data=None):
    # --- Initial Setup ---
    if not isinstance(data, pd.DataFrame): # Check if data is a DataFrame, allow empty
        print("Error: Input data is not a DataFrame.")
        # Optionally, create an empty PDF saying no data
        return

    # Check for essential columns needed early (allow generation even if some data is missing for certain parts)
    base_required_cols = ['区县', '站名', '总降雨量', '近一小时雨量']
    has_essential_cols = all(col in data.columns for col in base_required_cols)
    if not has_essential_cols:
        missing_cols = [col for col in base_required_cols if col not in data.columns]
        print(f"Warning: Input data missing essential columns for full report: {missing_cols}. Proceeding with available data.")
        # Don't return here, try to generate what's possible.

    data = data.copy() # Work on a copy to avoid modifying original DataFrame
    # Convert numeric columns, coercing errors (handle potential NaNs later)
    if '总降雨量' in data.columns:
        data['总降雨量'] = pd.to_numeric(data['总降雨量'], errors='coerce')
    if '近一小时雨量' in data.columns:
        data['近一小时雨量'] = pd.to_numeric(data['近一小时雨量'], errors='coerce')


    # --- Setup BaseDocTemplate with compact margins --- 
    doc = BaseDocTemplate(
        output_filename,
        pagesize=A4, # Default page size
        leftMargin=12 * mm,  # Reduced from 15mm
        rightMargin=12 * mm, # Reduced from 15mm
        topMargin=12 * mm,   # Reduced from 20mm for smaller header
        bottomMargin=8 * mm  # Reduced from 15mm (no footer needed)
    )

    styles = create_styles()
    if not styles:
        print("Error: Failed to create PDF styles.")
        return

    # --- Define Frames --- 
    # Portrait Frame (Standard page)
    frame_portrait = Frame(
        doc.leftMargin, 
        doc.bottomMargin, 
        doc.width, # width = pagesize[0] - leftMargin - rightMargin
        doc.height, # height = pagesize[1] - topMargin - bottomMargin
        id='portrait_frame'
    )
    # Landscape Frame (For map page)
    # Note: width/height are swapped for landscape
    frame_landscape = Frame(
        doc.leftMargin, 
        doc.bottomMargin, 
        doc.height, # Use page height (A4[1]) for landscape width calculation
        doc.width,  # Use page width (A4[0]) for landscape height calculation
        id='landscape_frame'
    )

    # --- Define Page Templates --- 
    portrait_template = PageTemplate(
        id='portrait',
        frames=[frame_portrait],
        onPage=header_footer, # Use the same header/footer function
        pagesize=A4
    )
    landscape_template = PageTemplate(
        id='landscape',
        frames=[frame_landscape],
        onPage=header_footer, # Use the same header/footer function
        pagesize=landscape(A4) # Set page size to landscape A4
    )

    # Add templates to the document
    doc.addPageTemplates([portrait_template, landscape_template])

    # --- Build Story (elements list) --- 
    elements = []

    # --- Page 1: 只显示表格内容 ---
   

    # Filter data for Pinggu
    pg_data_raw = data[data['区县'] == '平谷'].copy() if '区县' in data.columns else pd.DataFrame()
    has_essential_cols = all(col in data.columns for col in ['区县', '站名', '总降雨量', '近一小时雨量']) # Recheck here for clarity

    if not has_essential_cols or pg_data_raw.empty:
        elements.append(Paragraph('注意：未找到"平谷"区的有效数据或缺少必需列，无法生成平谷区详细统计。', styles['ErrorText']))
    else:
        pg_data = pg_data_raw.dropna(subset=['总降雨量', '近一小时雨量']).copy()

        if pg_data.empty:
             elements.append(Paragraph('注意：未找到"平谷"区有效的数值降水数据。', styles['ErrorText']))
        else:
            # 添加概要表格
            summary_table = create_summary_table(pg_data, query_start_time, query_end_time, doc.width, max_intensity_data)
            elements.append(summary_table)
            # 移除间距，让两个表格融为一体
            # Process Pinggu data first
            all_township_data = process_pg_data(pg_data) # Max_stations default is 3, but process_pg_data handles specific slots

            # --- Configuration for other districts to be integrated ---
            other_districts_config = [
                {"display_name": "密云区", "filter_name": "密云", "group_id": 4, "stations_to_show": 3},
                {"display_name": "顺义区", "filter_name": "顺义", "group_id": 4, "stations_to_show": 3},
                {"display_name": "兴隆县", "filter_name": "承德", "group_id": 4, "stations_to_show": 3}, # 你需要确认 "承德" 是否是兴隆县数据在CSV中的正确筛选名
                {"display_name": "蓟州区", "filter_name": "蓟州", "group_id": 5, "stations_to_show": 3}
            ]

            # 加载全部站点信息CSV文件以获取其他地区的完整站点列表
            try:
                all_stations_df = pd.read_csv('全部站点信息.csv')
            except Exception as e:
                print(f"Warning: Failed to load 全部站点信息.csv for other districts: {e}")
                all_stations_df = None

            # --- Process and append other districts data ---
            for config in other_districts_config:
                display_name_for_table = config["display_name"]
                filter_name_for_csv = config["filter_name"] # Use this for filtering
                group_id_val = config["group_id"]
                stations_to_show_count = config["stations_to_show"]

                # Filter data for the current other district using filter_name_for_csv
                if '区域内站点' in data.columns:
                    district_df_raw = data[(data['区县'] == filter_name_for_csv) & (data['区域内站点'] == '是')].copy()
                else:
                    print(f"Warning: '区域内站点' column missing. Processing all stations for {filter_name_for_csv} (displaying as {display_name_for_table}).")
                    district_df_raw = data[data['区县'] == filter_name_for_csv].copy()

                # 获取该地区在全部站点信息CSV中的总站点数和站点名称
                if all_stations_df is not None:
                    region_all_stations = all_stations_df[all_stations_df['区县'] == filter_name_for_csv].copy()
                    if '区域内站点' in region_all_stations.columns:
                        region_all_stations = region_all_stations[region_all_stations['区域内站点'] == '是']
                    total_station_count = len(region_all_stations)
                    all_station_names = region_all_stations['站名'].tolist()
                else:
                    # 如果CSV加载失败，回退到原有数据的站点数
                    total_station_count = len(district_df_raw) if not district_df_raw.empty else 0
                    all_station_names = district_df_raw['站名'].tolist() if not district_df_raw.empty else []

                if district_df_raw.empty:
                    print(f"Warning: No data found for CSV filter '{filter_name_for_csv}' (for {display_name_for_table}) or no '区域内站点' == '是'. Adding placeholder.")
                    processed_entry = {
                        'name': display_name_for_table, # Use display_name for the table
                        'avg_total_rain': float('nan'), 'avg_hourly_rain': float('nan'),
                        'total_station_count': total_station_count, 'stations': [],
                        'is_priority': False, 'group_id': group_id_val
                    }
                else:
                    # 不再使用 dropna，直接在原始数据上操作
                    district_df_with_total = district_df_raw[district_df_raw['总降雨量'].notna()].copy()
                    district_df_with_hourly = district_df_raw[district_df_raw['近一小时雨量'].notna()].copy()

                    # 计算平均值（只使用有效数据）
                    avg_total = district_df_with_total['总降雨量'].mean() if not district_df_with_total.empty else float('nan')
                    avg_hourly = district_df_with_hourly['近一小时雨量'].mean() if not district_df_with_hourly.empty else float('nan')
                    
                    station_list_dist = []

                    # 优先显示有总降雨量数据的站点，包括0
                    stations_to_sort = district_df_raw.copy()
                    # 创建一个临时排序列，将NaN值排在最后
                    stations_to_sort['sort_key'] = stations_to_sort['总降雨量'].fillna(float('-inf'))
                    stations_sorted = stations_to_sort.sort_values('sort_key', ascending=False)
                    
                    stations_to_display = stations_sorted.head(stations_to_show_count)
                    
                    for _, row in stations_to_display.iterrows():
                        station_list_dist.append({
                            'name': row['站名'],
                            'total_rain': row['总降雨量'],  # 可能是 0 或 NaN
                            'hourly_rain': row['近一小时雨量'] # 可能是 NaN
                        })

                    processed_entry = {
                        'name': display_name_for_table, # Use display_name for the table
                        'avg_total_rain': avg_total, 'avg_hourly_rain': avg_hourly,
                        'total_station_count': total_station_count, 'stations': station_list_dist,
                        'is_priority': False, 'group_id': group_id_val
                    }

                # 如果有效数据站点不足指定数量，从全部站点中选择其他站点补齐
                if len(processed_entry['stations']) < stations_to_show_count:
                    # 获取已经显示的站点名称
                    shown_stations = [s['name'] for s in processed_entry['stations']]
                    
                    # 从原始数据中查找其他站点（可能有部分数据）
                    remaining_stations_with_data = []
                    for _, row in district_df_raw.iterrows():
                        station_name = row['站名']
                        if station_name not in shown_stations:
                            remaining_stations_with_data.append({
                                'name': station_name,
                                'total_rain': row['总降雨量'],
                                'hourly_rain': row['近一小时雨量']
                            })
                    
                    # 如果原始数据中的站点还不够，从CSV中补充
                    if len(remaining_stations_with_data) < (stations_to_show_count - len(processed_entry['stations'])):
                        shown_and_remaining = shown_stations + [s['name'] for s in remaining_stations_with_data]
                        csv_only_stations = [name for name in all_station_names if name not in shown_and_remaining]
                        
                        for station_name in csv_only_stations:
                            remaining_stations_with_data.append({
                                'name': station_name,
                                'total_rain': float('nan'),
                                'hourly_rain': float('nan')
                            })
                    
                    # 补齐到指定数量
                    slots_needed = stations_to_show_count - len(processed_entry['stations'])
                    for i in range(slots_needed):
                        if i < len(remaining_stations_with_data):
                            processed_entry['stations'].append(remaining_stations_with_data[i])
                        else:
                            # 如果连剩余站点都不够，显示空行
                            processed_entry['stations'].append({
                                'name': '', 
                                'total_rain': float('nan'), 
                                'hourly_rain': float('nan')
                            })
                
                all_township_data.append(processed_entry)
                print(f"已添加其他地区数据: {display_name_for_table}, 总站点数: {total_station_count}, 显示站点数: {len(processed_entry['stations'])}")

            # 调试：打印融合后的数据总数
            print(f"融合后的总数据条目数: {len(all_township_data)}")
            print("数据条目名称:", [item['name'] for item in all_township_data])

            # 直接添加详细表格内容，不添加中间标题
            try:
                if '乡镇' not in pg_data.columns:
                     elements.append(Paragraph('错误: 平谷数据缺少"乡镇"列，无法按乡镇分组。', styles['ErrorText']))
                else:
                    # 注意：不要重新覆盖 all_township_data，它已经包含了其他地区数据
                    if all_township_data:
                        # 直接将表格内容添加到elements中，不使用中间标题
                        layout_items = create_three_column_township_layout(all_township_data, doc.width)
                        if isinstance(layout_items, list):
                            elements.extend(layout_items)
                        elif layout_items: # Check if it's a single valid flowable
                            elements.append(layout_items)
                    else:
                         elements.append(Paragraph("没有有效的平谷区乡镇数据可供处理和显示。", styles['CustomBodyText']))
            except Exception as e:
                print(f"Error creating Pinggu township table layout: {e}")
                elements.append(Paragraph(f'创建平谷乡镇表格时出错: {e}', styles['ErrorText']))

    # --- Page 3: Rainfall Map (Switch to Landscape) ---
    elements.append(NextPageTemplate('landscape')) # Switch to landscape template for the *next* page
    elements.append(PageBreak()) # Start the new page with the landscape template


    if map_image_buffer:
        try:
            print("PDF Gen: Processing map image buffer...")
            map_image_buffer.seek(0)
            buffer_size = len(map_image_buffer.read())
            map_image_buffer.seek(0)  # 重置指针
            print(f"PDF Gen: Map buffer size: {buffer_size} bytes")
            
            map_img = Image(map_image_buffer)
            print(f"PDF Gen: Successfully created Image object from buffer")

            # --- Calculate width and height to fit LANDSCAPE page ---
            img_width = map_img.imageWidth
            img_height = map_img.imageHeight
            
            # Validate image dimensions
            if img_width <= 0 or img_height <= 0:
                print(f"Warning: Invalid image dimensions: {img_width} x {img_height}")
                elements.append(Paragraph("地图图像尺寸无效，无法显示。", styles['ErrorText']))
            else:
                # Available width/height on landscape page (A4 landscape dimensions minus margins)
                # Add extra safety margin (20mm) to account for page header and other elements
                safety_margin = 20 * mm
                available_width_landscape = landscape(A4)[0] - doc.leftMargin - doc.rightMargin - safety_margin
                available_height_landscape = landscape(A4)[1] - doc.topMargin - doc.bottomMargin - safety_margin
                
                print(f"Image original size: {img_width} x {img_height}")
                print(f"Available space (with safety margin): {available_width_landscape} x {available_height_landscape}")
                
                # Calculate scale factors for both dimensions
                width_scale = available_width_landscape / img_width
                height_scale = available_height_landscape / img_height
                
                # Use the smaller scale factor to ensure image fits in both dimensions
                scale_factor = min(width_scale, height_scale)
                
                # Apply scaling
                map_img.drawWidth = img_width * scale_factor
                map_img.drawHeight = img_height * scale_factor
                
                print(f"Applied scale factor: {scale_factor}")
                print(f"Final image size: {map_img.drawWidth} x {map_img.drawHeight}")
                
                # Double-check that the scaled image fits within available space
                if map_img.drawWidth > available_width_landscape or map_img.drawHeight > available_height_landscape:
                    print(f"Warning: Scaled image still too large, applying additional reduction")
                    # Apply additional 10% reduction as fallback
                    map_img.drawWidth *= 0.9
                    map_img.drawHeight *= 0.9

                elements.append(map_img)
                
        except Exception as e:
            print(f"Error adding map image to PDF: {e}")
            elements.append(Paragraph(f"无法添加地图图像到PDF: {e}", styles['ErrorText']))
    else:
        elements.append(Paragraph("未能生成降水分布图或无相关数据。", styles['CustomBodyText']))


    # --- Build the PDF using BaseDocTemplate build method --- 
    try:
        doc.build(elements) # No need for onFirstPage/onLaterPages here, handled by PageTemplates
        print(f"Successfully generated report: {output_filename}")
    except Exception as e:
        print(f"Error generating PDF with BaseDocTemplate: {e}")

if __name__ == "__main__":
    try:
        data = pd.read_csv('pg_data.csv')
        # 测试用的降水强度数据
        test_intensity_data = {
            'station_name': '测试站点',
            'start_time': '14:00',
            'end_time': '15:00', 
            'intensity': 28.3
        }
        generate_pdf(data, query_start_time=datetime.now(), query_end_time=datetime.now(), map_image_buffer=None, output_filename="report_styled.pdf", max_intensity_data=test_intensity_data) # Use new filename
    except FileNotFoundError:
        print("Error: 'pg_data.csv' not found.")
    except KeyError as e:
        print(f"Error: Missing expected column in CSV - {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

