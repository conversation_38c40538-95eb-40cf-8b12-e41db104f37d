import logging
import os
import sys
import pandas as pd
import numpy as np

# 导入自定义模块 (改为相对导入)
from . import config
from . import data_io
from . import plotting

# 配置日志记录
logging.basicConfig(level=logging.INFO, format=config.LOGGING_FORMAT, stream=sys.stdout)
logger = logging.getLogger(__name__)

def create_dummy_dataframe() -> pd.DataFrame:
    """创建一个用于演示的示例 DataFrame"""
    data = {
        '站名': [f'站点{i}' for i in range(1, 6)],
        '经度': [117.0, 117.1, 117.3, 117.4, 117.2],
        '纬度': [40.1, 40.2, 40.15, 40.3, 40.05],
        '总降雨量': [25.5, 55.1, 120.0, 85.3, 15.7],
        '区县': ['平谷', '平谷', '密云', '平谷', '密云'],
        '乡镇': ['镇A', '镇B', '镇C', '镇A', '镇D'],
        '区域内站点': ['是', '是', '是', '否', '是']
    }
    df = pd.DataFrame(data)
    # 确保数据类型正确
    df['经度'] = pd.to_numeric(df['经度'], errors='coerce')
    df['纬度'] = pd.to_numeric(df['纬度'], errors='coerce')
    df['总降雨量'] = pd.to_numeric(df['总降雨量'], errors='coerce')
    return df

def main():
    """主执行函数 (示例用法)"""
    logger.info("开始执行降雨量地图生成程序 (示例模式)...")

    # 1. 准备 DataFrame (实际使用时，您需要自行加载或创建此 DataFrame)
    logger.info("创建示例 DataFrame...")
    rain_df = create_dummy_dataframe()
    logger.info(f"示例 DataFrame 创建完毕，包含 {len(rain_df)} 条记录。")
    print("示例 DataFrame 内容:")
    print(rain_df)

    # 2. 确定 DEM 文件路径
    # 注意：这里的路径是相对于执行脚本的位置，或者需要是绝对路径
    # 如果你的数据文件夹与 rainfall_map_generator 包在同一级，可以这样写：
    # dem_file_to_use = data_io.get_dem_filepath("高程/target_region_dem.tif", "高程/merged_dem.tif")
    # 或者直接使用 config 中的路径 (config 中的路径也是相对于执行位置的)
    dem_file_to_use = data_io.get_dem_filepath(config.TARGET_DEM_FILE, config.MERGED_DEM_FILE)
    if dem_file_to_use:
        logger.info(f"将使用 DEM 文件: {dem_file_to_use}")
    else:
        logger.warning("未找到可用的 DEM 文件，将不包含地形信息。")

    # 3. 生成地图 (使用准备好的 DataFrame)
    logger.info("开始生成降雨量地图...")
    image_buffer = plotting.generate_rainfall_map(rain_df, dem_file=dem_file_to_use)

    # 4. 处理输出
    if image_buffer:
        output_filename = "rainfall_map_example.png" # 输出到脚本执行的目录
        try:
            with open(output_filename, "wb") as f:
                f.write(image_buffer.getbuffer())
            logger.info(f"示例地图已成功保存为: {output_filename}")
        except IOError as e:
            logger.error(f"无法将示例地图保存到文件 '{output_filename}': {e}")
        except Exception as e:
            logger.exception(f"保存示例地图时发生未知错误: {e}")
    else:
        logger.error("示例地图生成失败，未保存任何文件。")

    logger.info("降雨量地图生成程序 (示例模式) 执行完毕。")

if __name__ == "__main__":
    # 注意：此 main 函数仅用于演示，实际使用时应从外部导入并调用 generate_rainfall_map
    main() 