""" Rainfall Map Generator Package """
import logging
import sys

# 使主绘图函数可以直接从包导入
from .plotting import generate_rainfall_map

# 配置包级别的日志记录器 (可选)
# 如果应用程序本身已经配置了日志，这里可以简化或移除
# 获取名为 'rainfall_map_generator' 的日志记录器
pkg_logger = logging.getLogger(__name__) 
# 防止没有配置日志处理程序时发出警告
if not pkg_logger.hasHandlers():
    pkg_logger.addHandler(logging.NullHandler())

# 可以定义 __all__ 来明确包的公共接口
__all__ = ['generate_rainfall_map']

__version__ = "0.1.0" # Optional: Define package version 