import matplotlib.colors as mcolors
import matplotlib as mpl
import cartopy.crs as ccrs

# --- 文件路径 ---
DATA_DIR = '.' # 根数据目录，可以根据需要修改
DEM_DIR = f'{DATA_DIR}/高程'
TARGET_DEM_FILE = f'{DEM_DIR}/target_region_dem.tif'
MERGED_DEM_FILE = f'{DEM_DIR}/merged_dem.tif' # 备用DEM文件
SHP_DIR = f'{DATA_DIR}/地图'
WATERSHED_SHP = f'{SHP_DIR}/流域.shp'
PINGGU_SHP = f'{SHP_DIR}/pinggu.shp'

# --- 字体设置 ---
FONT_SETTINGS = {
    'font.sans-serif': ['SimHei'],
    'axes.unicode_minus': False
}

# --- 地图范围 ---
DEFAULT_MAP_EXTENT = [116.75, 117.6, 39.95, 40.4] # 默认插值/地图范围

# --- 绘图参数 ---
FIG_SIZE = (12, 9)
GRID_RESOLUTION = 300j # 插值网格精度
PROJECTION = ccrs.PlateCarree()

# 降水等级和颜色
RAINFALL_LEVELS = [0.1, 10, 25, 50, 100, 250, 500]
RAINFALL_COLORS = ['#A9F090', '#40B73F', '#63B7FF', '#0000FE', '#FF00FC', '#850042']
RAINFALL_CMAP = mcolors.ListedColormap(RAINFALL_COLORS)
RAINFALL_NORM = mpl.colors.BoundaryNorm(RAINFALL_LEVELS, RAINFALL_CMAP.N)

# 地形晕渲参数
HILLSHADE_VMIN = 0
HILLSHADE_VMAX = 1500 # 可根据实际高程调整
HILLSHADE_AZDEG = 315
HILLSHADE_ALTDEG = 45
HILLSHADE_VERT_EXAG = 1
HILLSHADE_BLEND_MODE = 'soft'
HILLSHADE_CMAP = 'Greys'

# 边界样式
PINGGU_BOUNDARY_STYLE = {
    'facecolor': 'none',
    'edgecolor': 'black',
    'linewidth': 1.5,
    'zorder': 3
}
WATERSHED_BOUNDARY_STYLE = {
    'facecolor': 'none',
    'edgecolor': 'blue',
    'linewidth': 1.0,
    'zorder': 4
}

# 标注样式
STATION_LABEL_STYLE = {
    'fontsize': 7,
    'color': 'black',
    'ha': 'center',
    'va': 'bottom',
    'zorder': 10
}
PINGGU_STATION_MARKER_STYLE = {
    's': 10,
    'color': 'red',
    'zorder': 11
}
OTHER_STATION_MARKER_STYLE = {
    's': 10,
    'color': 'blue',
    'zorder': 11
}

# --- 其他 ---
OUTPUT_DPI = 300
LOGGING_FORMAT = '%(asctime)s - %(levelname)s - %(message)s' 

# --- 缓存设置 ---
# 启用缓存以提升地图生成性能
ENABLE_CACHING = True
# 缓存目录（相对于模块目录）
CACHE_DIR = 'cache' 