from matplotlib.path import Path
from matplotlib.patches import PathPatch
from cartopy.mpl.patch import geos_to_path
import logging
from . import data_io # 使用相对导入

# 获取 logger 实例
logger = logging.getLogger(__name__)

def clip_feature_with_shp(feature, ax, shp_fname: str):
    """使用 shapefile 对 matplotlib 特征 (如 ContourSet, AxesImage) 进行剪裁"""
    geometries = data_io.read_shapefile_geometries(shp_fname)
    if not geometries:
        logger.warning(f"无法从 {shp_fname} 获取地理对象，跳过剪裁。")
        return # 不进行剪裁

    try:
        # 从所有地理对象创建复合路径
        # 注意：如果 shapefile 包含多个不相邻的多边形，这可能需要更复杂的处理
        # 这里假设 geos_to_path 可以处理列表或需要迭代
        # 我们尝试将所有 geometry 合并到一个路径中
        paths = geos_to_path(geometries) # geos_to_path 返回一个路径列表
        if not paths:
             logger.warning(f"从 {shp_fname} 的地理对象转换路径失败。")
             return

        # 将多个路径合并为一个复合路径
        path = Path.make_compound_path(*paths)

        # 创建剪裁路径补丁
        patch = PathPatch(path, transform=ax.transData)

        # 应用剪裁
        feature.set_clip_path(patch)
        logger.info(f"已成功使用 {shp_fname} 对特征进行剪裁。")

    except Exception as e:
        # 捕获 geos_to_path 或 PathPatch 创建/应用过程中的任何错误
        logger.exception(f"从 {shp_fname} 创建或应用剪裁路径时出错: {e}")
        # 剪裁失败，但不应中断整个绘图过程 