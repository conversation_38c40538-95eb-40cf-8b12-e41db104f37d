import pandas as pd
import numpy as np
import rasterio
import cartopy.io.shapereader as shpreader
import logging
import os
from typing import Tuple, Optional, List, Dict, Any

# 获取 logger 实例
logger = logging.getLogger(__name__)

def read_dem_data(dem_file: str) -> Tuple[Optional[np.ma.MaskedArray], Optional[np.ndarray], Optional[np.ndarray], Optional[List[float]], Optional[int], Optional[int]]:
    """读取DEM数据并返回高程数据和坐标信息"""
    if not os.path.exists(dem_file):
        logger.warning(f"DEM 文件不存在: {dem_file}")
        return None, None, None, None, None, None
    try:
        with rasterio.open(dem_file) as dem:
            elevation = dem.read(1)
            # 处理NoData值
            nodata_value = dem.nodata
            if nodata_value is not None:
                elevation_masked = np.ma.masked_where(elevation == nodata_value, elevation)
            else:
                # 如果没有定义NoData值，假设所有数据都有效
                elevation_masked = np.ma.masked_array(elevation)
                logger.warning(f"DEM 文件 {dem_file} 未指定 NoData 值。")

            transform = dem.transform
            dem_extent = [dem.bounds.left, dem.bounds.right,
                          dem.bounds.bottom, dem.bounds.top]

            # 创建与DEM匹配的经纬度网格
            rows, cols = np.indices((dem.height, dem.width))
            xs, ys = rasterio.transform.xy(transform, rows.flatten(), cols.flatten())
            lon_grid = np.array(xs).reshape(dem.height, dem.width)
            lat_grid = np.array(ys).reshape(dem.height, dem.width)

            logger.info(f"成功读取 DEM 数据: {dem_file} ({dem.height}x{dem.width})")
            return elevation_masked, lon_grid, lat_grid, dem_extent, dem.height, dem.width

    except rasterio.RasterioIOError as e:
        logger.error(f"读取 DEM 文件时发生栅格IO错误: {e}")
        return None, None, None, None, None, None
    except Exception as e:
        logger.exception(f"读取 DEM 文件 '{dem_file}' 时发生未知错误: {e}")
        return None, None, None, None, None, None

def get_dem_filepath(target_dem: str, merged_dem: str) -> Optional[str]:
    """确定要使用的DEM文件路径，优先使用目标DEM"""
    if os.path.exists(target_dem):
        logger.info(f"使用目标 DEM 文件: {target_dem}")
        return target_dem
    elif os.path.exists(merged_dem):
        logger.warning(f"目标 DEM 文件 '{target_dem}' 不存在，尝试使用备用 DEM 文件: {merged_dem}")
        return merged_dem
    else:
        logger.error(f"目标 DEM 文件 '{target_dem}' 和备用 DEM 文件 '{merged_dem}' 均不存在。")
        return None

def read_shapefile_geometries(shp_fname: str) -> Optional[List[Any]]:
    """读取 Shapefile 并返回地理对象列表"""
    if not os.path.exists(shp_fname):
        logger.warning(f"Shapefile 文件不存在: {shp_fname}")
        return None
    try:
        reader = shpreader.Reader(shp_fname)
        geometries = list(reader.geometries())
        reader.close()
        if not geometries:
            logger.warning(f"在 Shapefile '{shp_fname}' 中未找到地理对象。")
            return None # 或者返回空列表，取决于后续逻辑
        logger.info(f"成功读取 Shapefile: {shp_fname}，找到 {len(geometries)} 个地理对象。")
        return geometries
    except Exception as e:
        logger.exception(f"读取 Shapefile '{shp_fname}' 时出错: {e}")
        return None 