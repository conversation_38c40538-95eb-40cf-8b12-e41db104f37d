import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
import cartopy.crs as ccrs
import cartopy.feature as cfeature
from cartopy.mpl.gridliner import LONGITUDE_FORMATTER, LATITUDE_FORMATTER
from scipy.interpolate import Rbf
from matplotlib.colors import LightSource
import logging
import io
from typing import Optional, Tuple, List, Any
import os
import hashlib
from hashlib import sha256

# 导入自定义模块 (改为相对导入)
from . import config
from . import data_io
from . import gis_utils

# 获取 logger 实例
logger = logging.getLogger(__name__)

def _get_cache_dir() -> str:
    """获取缓存目录路径"""
    cache_dir = os.path.join(os.path.dirname(__file__), config.CACHE_DIR)
    os.makedirs(cache_dir, exist_ok=True)
    return cache_dir

def _get_data_hash(rain_data: pd.DataFrame) -> str:
    """根据降雨数据生成哈希值用于缓存"""
    # 只使用影响插值的关键列
    key_cols = ['经度', '纬度', '总降雨量']
    if not all(col in rain_data.columns for col in key_cols):
        return "invalid_data"
    
    # 生成数据内容的哈希
    data_str = rain_data[key_cols].to_string()
    return sha256(data_str.encode('utf-8')).hexdigest()[:16]

def _get_grid_hash(grid_x: np.ndarray, grid_y: np.ndarray) -> str:
    """根据插值网格生成哈希值"""
    grid_info = f"{grid_x.shape}_{grid_y.shape}_{grid_x.min():.6f}_{grid_x.max():.6f}_{grid_y.min():.6f}_{grid_y.max():.6f}"
    return hashlib.md5(grid_info.encode()).hexdigest()[:8]

def _setup_map_axis(fig, extent: List[float], projection):
    """初始化地图画布和坐标轴"""
    ax = fig.add_subplot(1, 1, 1, projection=projection)
    ax.set_facecolor('white')
    ax.set_extent(extent, crs=projection)

    # 添加地图特征
    ax.add_feature(cfeature.BORDERS, linestyle=':')
    ax.add_feature(cfeature.COASTLINE)
    ax.add_feature(cfeature.RIVERS)

    # 添加网格线
    gl = ax.gridlines(crs=projection, draw_labels=True,
                     linewidth=0.5, color='gray', alpha=0.5, linestyle='--')
    gl.top_labels = False
    gl.right_labels = False
    gl.xformatter = LONGITUDE_FORMATTER
    gl.yformatter = LATITUDE_FORMATTER
    return ax

def _perform_interpolation(lon: np.ndarray, lat: np.ndarray, values: np.ndarray,
                         grid_x: np.ndarray, grid_y: np.ndarray) -> Optional[np.ndarray]:
    """执行 Rbf 插值（带缓存功能）"""
    # 过滤掉无效的经纬度或降雨量数据 (例如 NaN)
    valid_indices = ~np.isnan(lon) & ~np.isnan(lat) & ~np.isnan(values)
    lon_valid = lon[valid_indices]
    lat_valid = lat[valid_indices]
    values_valid = values[valid_indices]

    if len(lon_valid) < 3:
        logger.error("有效的站点数据不足（少于3个），无法进行插值。")
        return None

    # 尝试从缓存加载插值结果
    if config.ENABLE_CACHING:
        try:
            # 创建用于缓存的数据DataFrame
            cache_data = pd.DataFrame({
                '经度': lon_valid,
                '纬度': lat_valid, 
                '总降雨量': values_valid
            })
            
            data_hash = _get_data_hash(cache_data)
            grid_hash = _get_grid_hash(grid_x, grid_y)
            cache_filename = f"interpolation_{data_hash}_{grid_hash}.npz"
            cache_path = os.path.join(_get_cache_dir(), cache_filename)
            
            if os.path.exists(cache_path):
                logger.info(f"从缓存加载插值数据: {cache_filename}")
                cache_data_loaded = np.load(cache_path)
                return cache_data_loaded['grid_z']
        except Exception as e:
            logger.warning(f"读取插值缓存时出错: {e}，将重新计算")

    try:
        # 使用 'linear' 或 'cubic' 等插值函数
        # 注意 'multiquadric', 'inverse', 'gaussian', 'quintic' 可能对数据分布敏感
        rbf = Rbf(lon_valid, lat_valid, values_valid, function='linear', smooth=0) # smooth=0 for exact interpolation
        grid_z = rbf(grid_x, grid_y)
        logger.info("成功执行 Rbf 插值。")
        
        # 保存到缓存
        if config.ENABLE_CACHING:
            try:
                cache_data = pd.DataFrame({
                    '经度': lon_valid,
                    '纬度': lat_valid, 
                    '总降雨量': values_valid
                })
                data_hash = _get_data_hash(cache_data)
                grid_hash = _get_grid_hash(grid_x, grid_y)
                cache_filename = f"interpolation_{data_hash}_{grid_hash}.npz"
                cache_path = os.path.join(_get_cache_dir(), cache_filename)
                
                np.savez_compressed(cache_path, grid_z=grid_z)
                logger.info(f"插值数据已缓存到: {cache_filename}")
            except Exception as e:
                logger.warning(f"保存插值缓存时出错: {e}")
        
        return grid_z
    except ValueError as e:
        logger.error(f"插值错误: {e}. 请检查输入数据（可能站点共线或数量不足）。")
        return None
    except Exception as e:
        logger.exception(f"插值时发生未知错误: {e}")
        return None

def _add_hillshade(ax, dem_data: Tuple[np.ma.MaskedArray, np.ndarray, np.ndarray, List[float], int, int], projection):
    """添加地形晕渲图层（带缓存功能）"""
    elevation_masked, _, _, dem_extent, _, _ = dem_data
    if not isinstance(elevation_masked, np.ma.MaskedArray) or dem_extent is None:
        logger.warning("缺少有效的 DEM 数据或范围，无法添加地形晕渲。")
        return None

    # 尝试从缓存加载地形晕渲
    rgb = None
    if config.ENABLE_CACHING:
        try:
            # 生成DEM数据的哈希值用于缓存
            dem_hash = hashlib.md5(elevation_masked.data.tobytes()).hexdigest()[:16]
            cache_filename = f"hillshade_{dem_hash}.npz"
            cache_path = os.path.join(_get_cache_dir(), cache_filename)
            
            if os.path.exists(cache_path):
                logger.info(f"从缓存加载地形晕渲: {cache_filename}")
                cache_data = np.load(cache_path)
                rgb = cache_data['rgb']
        except Exception as e:
            logger.warning(f"读取地形晕渲缓存时出错: {e}，将重新计算")

    # 如果缓存不存在或加载失败，重新计算
    if rgb is None:
        try:
            ls = LightSource(azdeg=config.HILLSHADE_AZDEG, altdeg=config.HILLSHADE_ALTDEG)
            # Ensure data type is suitable for shading and handle potential all-masked arrays
            if elevation_masked.mask.all():
                 logger.warning("DEM 数据全部被掩膜，无法计算晕渲。")
                 return None
            elevation_float = elevation_masked.filled(np.nan).astype(float) # Fill masked values for shading calculation

            rgb = ls.shade(elevation_float, cmap=plt.get_cmap(config.HILLSHADE_CMAP),
                          vert_exag=config.HILLSHADE_VERT_EXAG, blend_mode=config.HILLSHADE_BLEND_MODE)

            # 保存到缓存
            if config.ENABLE_CACHING:
                try:
                    dem_hash = hashlib.md5(elevation_masked.data.tobytes()).hexdigest()[:16]
                    cache_filename = f"hillshade_{dem_hash}.npz"
                    cache_path = os.path.join(_get_cache_dir(), cache_filename)
                    
                    np.savez_compressed(cache_path, rgb=rgb)
                    logger.info(f"地形晕渲已缓存到: {cache_filename}")
                except Exception as e:
                    logger.warning(f"保存地形晕渲缓存时出错: {e}")

        except Exception as e:
            logger.exception(f"计算地形晕渲时出错: {e}")
            return None

    try:
        terrain_plot = ax.imshow(rgb, origin='upper', extent=dem_extent,
                               transform=projection, zorder=1,
                               interpolation='bilinear') # Bilinear is smoother
        logger.info("成功添加地形晕渲图层。")
        return terrain_plot
    except Exception as e:
        logger.exception(f"添加地形晕渲图层时出错: {e}")
        return None

def _add_rainfall_contours(ax, grid_x, grid_y, grid_z, projection):
    """添加降雨量等值线填充图"""
    try:
        # Ensure grid_z doesn't contain NaNs where plotting occurs, fill with a value outside the levels range (e.g., -1)
        grid_z_filled = np.nan_to_num(grid_z, nan=-1.0)

        contour = ax.contourf(grid_x, grid_y, grid_z_filled, levels=config.RAINFALL_LEVELS,
                             cmap=config.RAINFALL_CMAP, norm=config.RAINFALL_NORM,
                             extend='max', transform=projection, alpha=0.5, zorder=2)
        logger.info("成功添加降雨量等值线图层。")
        return contour
    except Exception as e:
        logger.exception(f"绘制降雨量等值线图时出错: {e}")
        return None

def _add_boundaries(ax, shp_fname: str, style: dict, projection):
    """在地图上绘制 Shapefile 边界"""
    geometries = data_io.read_shapefile_geometries(shp_fname)
    if geometries:
        try:
            ax.add_geometries(geometries, crs=projection, **style)
            logger.info(f"成功绘制边界: {shp_fname}")
        except Exception as e:
            logger.exception(f"绘制边界文件 {shp_fname} 时出错: {e}")
    # else: # Warning already logged in data_io.read_shapefile_geometries
    #     pass

def _add_station_labels(ax, rain_data: pd.DataFrame, projection):
    """在地图上标注站点信息"""
    # --- 标注平谷区各乡镇最大降雨量站点 ---
    try:
        pinggu_data = rain_data.loc[rain_data['区县'] == '平谷'].copy()
        required_cols_pg = ['乡镇', '站名', '经度', '纬度', '总降雨量']
        if not all(col in pinggu_data.columns for col in required_cols_pg):
            missing_cols = [col for col in required_cols_pg if col not in pinggu_data.columns]
            logger.warning(f"平谷数据中缺少标注所需列: {missing_cols}，无法进行标注。")
        elif not pinggu_data.dropna(subset=['乡镇', '总降雨量']).empty:
            # Find index of max rainfall per town, handling potential NaNs in max value
            idx = pinggu_data.loc[pinggu_data.dropna(subset=['总降雨量']).groupby('乡镇')['总降雨量'].idxmax()].index
            max_rain_stations = pinggu_data.loc[idx]

            processed_stations = 0
            for index, row in max_rain_stations.iterrows():
                lon, lat, rain, name = row['经度'], row['纬度'], row['总降雨量'], row['站名']
                # Check all required values are valid before plotting
                if pd.notna([lon, lat, rain, name]).all():
                    ax.scatter(lon, lat, **config.PINGGU_STATION_MARKER_STYLE, transform=projection)
                    label_text = f"{name}\n{rain:.1f}"
                    ax.text(lon, lat, label_text, **config.STATION_LABEL_STYLE, transform=projection)
                    processed_stations += 1
            if processed_stations > 0:
                 logger.info(f"完成标注 {processed_stations} 个平谷区乡镇最大降雨站点。")
            else:
                 logger.info("平谷区数据中未找到符合条件的站点进行标注。")
        else:
             logger.info("平谷区数据为空或缺少关键分组信息 (乡镇/总降雨量)，不进行标注。")

    except KeyError as e:
        logger.warning(f"标注平谷站点时发生键错误 '{e}'，可能缺少必要的列。")
    except Exception as e:
        logger.exception(f"标注平谷区站点时发生未知错误: {e}")

    # --- 标注其他区县流域内最大降雨量站点 ---
    try:
        required_cols_other = ['区县', '站名', '经度', '纬度', '总降雨量', '区域内站点']
        if not all(col in rain_data.columns for col in required_cols_other):
             missing_cols = [col for col in required_cols_other if col not in rain_data.columns]
             logger.warning(f"数据中缺少标注其他区县所需列: {missing_cols}，无法进行标注。")
             return # Exit this part if essential columns are missing

        # Filter for other districts AND '区域内站点' == '是'
        other_data_mask = (rain_data['区县'] != '平谷') & (rain_data['区域内站点'] == '是')
        other_data = rain_data.loc[other_data_mask].copy()

        if not other_data.dropna(subset=['区县', '总降雨量']).empty:
            # Find index of max rainfall per district, handling potential NaNs in max value
            idx = other_data.loc[other_data.dropna(subset=['总降雨量']).groupby('区县')['总降雨量'].idxmax()].index
            max_rain_stations_other = other_data.loc[idx]

            processed_stations_other = 0
            for index, row in max_rain_stations_other.iterrows():
                lon, lat, rain, s_name = row['经度'], row['纬度'], row['总降雨量'], row['站名']
                # Check all required values are valid before plotting
                if pd.notna([lon, lat, rain, s_name]).all():
                    ax.scatter(lon, lat, **config.OTHER_STATION_MARKER_STYLE, transform=projection)
                    label_text = f"{s_name}\n{rain:.1f}"
                    ax.text(lon, lat, label_text, **config.STATION_LABEL_STYLE, transform=projection)
                    processed_stations_other += 1
            if processed_stations_other > 0:
                logger.info(f"完成标注 {processed_stations_other} 个其他区县流域内最大降雨站点。")
            else:
                logger.info("其他区县流域内未找到符合条件的站点进行标注。")
        else:
             logger.info("其他区县流域内无有效站点数据 ('区域内站点' == '是' 且有区县/总降雨量)，不进行标注。")

    except KeyError as e:
        logger.warning(f"标注其他区县站点时发生键错误 '{e}'，可能缺少必要的列。")
    except Exception as e:
        logger.exception(f"标注其他区县站点时发生未知错误: {e}")


def _finalize_plot(fig, ax, contour, extent: List[float], dem_data: Optional[Tuple[np.ma.MaskedArray, Any, Any, Any, Any, Any]] = None) -> Optional[io.BytesIO]:
    """添加色标、标题、信息文本并保存图像到内存"""
    # 添加色标
    if contour:
        try:
            cbar = plt.colorbar(contour, ax=ax, orientation='horizontal',
                              pad=0.08, fraction=0.05, aspect=40)
            cbar.set_label('降水量 (mm)')
        except Exception as e:
             logger.warning(f"添加色标时出错: {e}")
    else:
         logger.warning("无法添加色标，因为降雨等值线图对象无效。")

    # 设置标题 (固定标题)
    title = '泃河流域降水分布图'
    plt.title(title, fontsize=16, color='red')

    # 不再添加地理范围信息文本框
    # x_min, x_max, y_min, y_max = extent
    # info_text = f"地理范围：\n经度：{x_min:.4f}°E - {x_max:.4f}°E\n纬度：{y_min:.4f}°N - {y_max:.4f}°N"
    # ... (移除 figtext 相关代码) ...

    # 保存图片到内存
    plt.tight_layout()
    try:
        buf = io.BytesIO()
        plt.savefig(buf, format='png', dpi=config.OUTPUT_DPI)
        plt.close(fig) # 关闭图形，释放内存
        buf.seek(0)
        logger.info("图像已成功保存到内存缓冲区。")
        return buf
    except Exception as e:
        logger.exception(f"保存图像到内存时出错: {e}")
        plt.close(fig) # 确保即使保存失败也关闭图形
        return None

def generate_rainfall_map(rain_data: pd.DataFrame) -> Optional[io.BytesIO]:
    """
    生成降雨量分布图（可选叠加地形晕渲，根据配置自动决定）。
    所有配置项（如DEM文件, 样式, 范围等）均从 config.py 读取。

    Args:
        rain_data: 包含降雨数据的 Pandas DataFrame。
                   必需列: '经度', '纬度', '总降雨量'。
                   标注所需列: '区县', '乡镇', '站名', '区域内站点' (如果数据包含这些列)。
                   列中的无效值 (如 non-numeric) 应提前处理或将导致错误/警告。

    Returns:
        包含 PNG 图像数据的 BytesIO 对象，如果生成失败则返回 None。
    """

    # --- 1. 初始化绘图和字体 ---
    logger.info("初始化绘图环境...")
    # Set backend explicitly to avoid potential issues in different environments
    mpl.use('Agg') # Use Agg backend for non-interactive plotting
    plt.style.use('default') # Reset styles
    mpl.rcParams.update(config.FONT_SETTINGS)

    # --- 2. 查找并加载 DEM 数据 (根据配置) ---
    dem_data_tuple: Optional[Tuple[np.ma.MaskedArray, np.ndarray, np.ndarray, List[float], int, int]] = None
    map_extent = list(config.DEFAULT_MAP_EXTENT) # Use list for potential modification
    dem_lon_grid, dem_lat_grid = None, None

    # 内部决定 DEM 文件路径
    dem_file_to_use = data_io.get_dem_filepath(config.TARGET_DEM_FILE, config.MERGED_DEM_FILE)

    if dem_file_to_use:
        logger.info(f"尝试加载配置的 DEM 数据: {dem_file_to_use}")
        dem_results = data_io.read_dem_data(dem_file_to_use)
        # Check if elevation data (first element) is valid MaskedArray
        if dem_results[0] is not None and isinstance(dem_results[0], np.ma.MaskedArray):
            dem_data_tuple = dem_results
            dem_lon_grid, dem_lat_grid = dem_data_tuple[1], dem_data_tuple[2]
            # Use DEM extent if valid (list of 4 numbers)
            if dem_data_tuple[3] and len(dem_data_tuple[3]) == 4 and all(isinstance(x, (int, float)) for x in dem_data_tuple[3]):
                 map_extent = dem_data_tuple[3]
                 logger.info(f"使用 DEM 数据的范围作为地图范围: {map_extent}")
            else:
                logger.warning(f"DEM 范围无效 ({dem_data_tuple[3]})，将使用默认地图范围: {map_extent}")
        else:
             logger.warning(f"无法加载有效的 DEM 高程数据，将不显示地形晕渲，使用默认地图范围: {map_extent}")
    else:
         logger.info(f"未在配置中找到有效的 DEM 文件路径，将不显示地形晕渲，使用默认地图范围: {map_extent}")

    # --- 3. 准备插值网格 ---
    if dem_lon_grid is None or dem_lat_grid is None:
        logger.info(f"未从 DEM 获取网格，将创建分辨率为 {config.GRID_RESOLUTION} 的规则网格，范围: {map_extent}")
        x_min, x_max, y_min, y_max = map_extent
        try:
            # Ensure extent values are valid before creating grid
            if not all(isinstance(x, (int, float)) for x in map_extent):
                 raise ValueError("地图范围值无效")
            grid_x, grid_y = np.mgrid[x_min:x_max:config.GRID_RESOLUTION, y_min:y_max:config.GRID_RESOLUTION]
        except (TypeError, ValueError) as e:
             logger.error(f"创建插值网格时出错（检查地图范围 {map_extent} 和分辨率 {config.GRID_RESOLUTION}）: {e}")
             return None
    else:
        logger.info("使用 DEM 数据的经纬度网格进行插值。")
        grid_x, grid_y = dem_lon_grid, dem_lat_grid

    # --- 4. 检查并执行插值 ---
    logger.info("准备执行插值...")
    required_interp_cols = ['经度', '纬度', '总降雨量']
    if not all(col in rain_data.columns for col in required_interp_cols):
        logger.error(f"降雨数据缺少插值所需的列: {[c for c in required_interp_cols if c not in rain_data.columns]}")
        return None
    if rain_data.empty:
        logger.error("输入的降雨数据 DataFrame 为空。")
        return None
    # Drop rows with NaN in essential columns before interpolation
    rain_data_clean = rain_data.dropna(subset=required_interp_cols).copy()
    if rain_data_clean.empty:
         logger.error("清理无效值后，无有效降雨数据用于插值。")
         return None
    if len(rain_data_clean) < 3:
        logger.warning(f"清理无效值后，仅剩 {len(rain_data_clean)} 个有效站点，可能影响插值质量。")


    lon = rain_data_clean['经度'].values
    lat = rain_data_clean['纬度'].values
    values = rain_data_clean['总降雨量'].values
    grid_z = _perform_interpolation(lon, lat, values, grid_x, grid_y)
    if grid_z is None:
        logger.error("插值失败，无法生成地图。")
        return None # Interpolation failure is critical

    # --- 5. 开始绘图 ---
    logger.info("开始设置地图画布...")
    fig = plt.figure(figsize=config.FIG_SIZE)
    try:
        ax = _setup_map_axis(fig, map_extent, config.PROJECTION)
    except Exception as e:
        logger.exception(f"设置地图坐标轴时出错: {e}")
        plt.close(fig)
        return None

    # --- 6. 添加地形晕渲 (如果DEM数据有效) ---
    terrain_plot = None
    if dem_data_tuple:
        logger.info("添加地形晕渲图层...")
        terrain_plot = _add_hillshade(ax, dem_data_tuple, config.PROJECTION)
        # Clip hillshade if successfully added and clipping shapefile exists
        if terrain_plot and config.WATERSHED_SHP and os.path.exists(config.WATERSHED_SHP):
            logger.info(f"使用 {config.WATERSHED_SHP} 裁剪地形图层...")
            gis_utils.clip_feature_with_shp(terrain_plot, ax, config.WATERSHED_SHP)
        elif terrain_plot:
             logger.warning(f"无法裁剪地形图层，因为裁剪边界文件 '{config.WATERSHED_SHP}' 未配置或不存在。")

    # --- 7. 添加降雨量等值线 ---
    logger.info("添加降雨量等值线图层...")
    contour_plot = _add_rainfall_contours(ax, grid_x, grid_y, grid_z, config.PROJECTION)
    # Clip contours if successfully added and clipping shapefile exists
    if contour_plot and config.WATERSHED_SHP and os.path.exists(config.WATERSHED_SHP):
        logger.info(f"使用 {config.WATERSHED_SHP} 裁剪降雨量图层...")
        gis_utils.clip_feature_with_shp(contour_plot, ax, config.WATERSHED_SHP)
    elif contour_plot:
         logger.warning(f"无法裁剪降雨量图层，因为裁剪边界文件 '{config.WATERSHED_SHP}' 未配置或不存在。")
    else:
        # If contouring failed, we can't proceed meaningfully
        logger.error("降雨量等值线图绘制失败，无法完成地图生成。")
        plt.close(fig)
        return None # Core layer failure

    # --- 8. 添加边界 (从配置读取) ---
    logger.info("添加边界图层...")
    if config.PINGGU_SHP and os.path.exists(config.PINGGU_SHP):
        _add_boundaries(ax, config.PINGGU_SHP, config.PINGGU_BOUNDARY_STYLE, config.PROJECTION)
    else:
        logger.warning(f"平谷边界文件 '{config.PINGGU_SHP}' 未配置或不存在，跳过绘制。")

    if config.WATERSHED_SHP and os.path.exists(config.WATERSHED_SHP):
        _add_boundaries(ax, config.WATERSHED_SHP, config.WATERSHED_BOUNDARY_STYLE, config.PROJECTION)
    else:
         logger.warning(f"流域边界文件 '{config.WATERSHED_SHP}' 未配置或不存在，跳过绘制。")

    # --- 9. 添加站点标注 ---
    logger.info("添加站点标注...")
    _add_station_labels(ax, rain_data, config.PROJECTION) # Pass original rain_data for labeling

    # --- 10. 完成并保存图像 ---
    logger.info("完成图像并保存到内存...")
    image_buffer = _finalize_plot(fig, ax, contour_plot, map_extent, dem_data_tuple)

    logger.info("generate_rainfall_map 执行完毕。")
    return image_buffer 