#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
降水查询系统全流程架构图 - matplotlib版本
基于 precipitation_app_improved.py 的代码分析
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def create_precipitation_flowchart():
    """创建降水查询系统流程图"""
    fig, ax = plt.subplots(1, 1, figsize=(24, 30))
    ax.set_xlim(0, 24)
    ax.set_ylim(0, 36)
    ax.axis('off')
    
    # 定义颜色方案
    colors = {
        'init': '#E3F2FD',        # 浅蓝色 - 系统初始化
        'ui': '#E8F5E8',          # 浅绿色 - 用户界面
        'data': '#FFF3E0',        # 浅橙色 - 数据处理
        'display': '#F3E5F5',     # 浅紫色 - 数据展示
        'edit': '#FFEBEE',        # 浅红色 - 数据编辑
        'report': '#E0F2F1',      # 浅青色 - 报告生成
        'api': '#FFF8E1',         # 浅黄色 - API调用
        'error': '#FFCDD2'        # 浅粉色 - 错误处理
    }
    
    # 绘制标题
    ax.text(12, 35, '🌧️ 降水查询系统全流程架构图', 
            fontsize=24, fontweight='bold', ha='center')
    ax.text(12, 34, 'precipitation_app_improved.py 系统架构分析', 
            fontsize=14, ha='center', style='italic')
    
    # 1. 系统初始化层 (顶部)
    draw_box(ax, 1, 31, 22, 2, '🚀 系统初始化层', colors['init'])
    draw_box(ax, 2, 30, 4, 0.8, '📦 模块导入\nimport streamlit', colors['init'])
    draw_box(ax, 6.5, 30, 4, 0.8, '⚙️ 页面配置\nset_page_config', colors['init'])
    draw_box(ax, 11, 30, 4, 0.8, '🎨 CSS样式\ncustom styling', colors['init'])
    draw_box(ax, 15.5, 30, 4, 0.8, '📊 Session State\n初始化变量', colors['init'])
    draw_box(ax, 20, 30, 2.5, 0.8, '🔧 工具函数\nget_max_hourly', colors['init'])
    
    # 2. 用户界面层 (上部)
    draw_box(ax, 1, 27, 22, 2, '🖥️ 用户界面层', colors['ui'])
    draw_box(ax, 2, 26, 5, 0.8, '🏠 主页面渲染\n标题和副标题', colors['ui'])
    draw_box(ax, 7.5, 26, 5, 0.8, '📋 侧边栏控件\n查询条件设置', colors['ui'])
    draw_box(ax, 13, 26, 4, 0.8, '🔘 查询按钮\nquery_button', colors['ui'])
    draw_box(ax, 17.5, 26, 5, 0.8, '💳 统计卡片\nmetric cards', colors['ui'])
    
    # 3. API调用和数据获取层 (中上部)
    draw_box(ax, 1, 23.5, 22, 2, '🌐 API调用和数据获取层', colors['api'])
    draw_box(ax, 2, 22.5, 6, 0.8, '🔄 Station API调用\nrun_station_api()', colors['api'])
    draw_box(ax, 8.5, 22.5, 6, 0.8, '📊 平谷最大小时雨量\nget_pinggu_max_hourly_rain', colors['api'])
    draw_box(ax, 15, 22.5, 7.5, 0.8, '⚠️ 错误处理和重试机制\ntry-except blocks', colors['error'])
    
    # 4. 数据处理层 (中部)
    draw_box(ax, 1, 19.5, 22, 2.5, '🔧 数据处理层', colors['data'])
    draw_box(ax, 2, 20.5, 4.5, 0.8, '🧹 数据清洗\n类型转换', colors['data'])
    draw_box(ax, 7, 20.5, 4.5, 0.8, '✅ 数据验证\n索引检查', colors['data'])
    draw_box(ax, 12, 20.5, 4.5, 0.8, '📍 数据分区\n平谷/其他区域', colors['data'])
    draw_box(ax, 17, 20.5, 5.5, 0.8, '📈 统计计算\nmax/avg/count', colors['data'])
    draw_box(ax, 2, 19.5, 10, 0.8, '🏷️ 数据标记: use_data, problem 字段管理', colors['data'])
    draw_box(ax, 12.5, 19.5, 10, 0.8, '🔄 数据更新函数: update_raw_data_from_edits()', colors['data'])
    
    # 5. 数据展示层 (中下部)
    draw_box(ax, 1, 16, 22, 2.5, '📊 数据展示层', colors['display'])
    draw_box(ax, 2, 17, 6.5, 0.8, '📋 表格视图\nst.data_editor 可编辑表格', colors['display'])
    draw_box(ax, 9, 17, 6.5, 0.8, '🗺️ 地图视图\nrainfall_map_generator', colors['display'])
    draw_box(ax, 16, 17, 6.5, 0.8, '🏷️ 标签页切换\nst.tabs 区域分组', colors['display'])
    draw_box(ax, 2, 16, 10, 0.8, '🎛️ 显示控制: show_all_data 选项控制数据过滤', colors['display'])
    draw_box(ax, 12.5, 16, 10, 0.8, '📈 实时统计: 动态计算和显示关键指标', colors['display'])
    
    # 6. 数据编辑层 (下部)
    draw_box(ax, 1, 12.5, 22, 2.5, '✏️ 数据编辑层', colors['edit'])
    draw_box(ax, 2, 13.5, 5, 0.8, '📝 平谷区数据编辑\npg_data_editor', colors['edit'])
    draw_box(ax, 7.5, 13.5, 5, 0.8, '📝 其他区域数据编辑\nother_data_editor', colors['edit'])
    draw_box(ax, 13, 13.5, 4.5, 0.8, '💾 保存修改按钮\nsave_changes', colors['edit'])
    draw_box(ax, 18, 13.5, 4.5, 0.8, '🔄 数据同步机制\nsession_state更新', colors['edit'])
    draw_box(ax, 2, 12.5, 10, 0.8, '🛡️ 编辑保护: 防重复执行标志 is_updating_data', colors['edit'])
    draw_box(ax, 12.5, 12.5, 10, 0.8, '✅ 数据验证: 编辑后类型转换和完整性检查', colors['edit'])
    
    # 7. 报告生成层 (底部)
    draw_box(ax, 1, 8.5, 22, 3, '📄 报告生成层', colors['report'])
    draw_box(ax, 2, 10.5, 5, 0.8, '📋 数据完整性检查\n必需列验证', colors['report'])
    draw_box(ax, 7.5, 10.5, 5, 0.8, '🗺️ 地图缓存管理\nmap_image_buffer', colors['report'])
    draw_box(ax, 13, 10.5, 4.5, 0.8, '📊 PDF数据准备\n过滤和格式化', colors['report'])
    draw_box(ax, 18, 10.5, 4.5, 0.8, '📄 PDF生成调用\ngenerate_pdf', colors['report'])
    draw_box(ax, 2, 9.5, 10, 0.8, '💾 文件管理: PDF目录创建和临时文件处理', colors['report'])
    draw_box(ax, 12.5, 9.5, 10, 0.8, '📥 下载功能: st.download_button 和预览功能', colors['report'])
    draw_box(ax, 2, 8.5, 20.5, 0.8, '🔄 状态管理: pdf_generated, pdf_filename, show_preview 等状态变量', colors['report'])
    
    # 8. 系统保障机制 (最底部)
    draw_box(ax, 1, 5, 22, 2.5, '🛡️ 系统保障机制', colors['error'])
    draw_box(ax, 2, 6.5, 5, 0.8, '📝 日志记录\nlogging系统', colors['error'])
    draw_box(ax, 7.5, 6.5, 5, 0.8, '⚠️ 异常处理\ntry-except覆盖', colors['error'])
    draw_box(ax, 13, 6.5, 4.5, 0.8, '🔄 状态恢复\nsession_state管理', colors['error'])
    draw_box(ax, 18, 6.5, 4.5, 0.8, '🚨 用户提示\nst.error/warning', colors['error'])
    draw_box(ax, 2, 5.5, 10, 0.8, '🔒 数据保护: 原始数据备份和编辑隔离机制', colors['error'])
    draw_box(ax, 12.5, 5.5, 10, 0.8, '⚡ 性能优化: 缓存机制和重复操作防护', colors['error'])
    draw_box(ax, 2, 5, 20.5, 0.8, '🎯 用户体验: 进度提示、操作反馈和错误恢复指导', colors['error'])
    
    # 9. 关键数据流标注 (右侧)
    draw_box(ax, 1, 2, 22, 2, '🔄 关键数据流路径', colors['data'])
    ax.text(2, 3.5, '1. 用户输入 → 查询参数 → API调用 → raw_data', fontsize=10, ha='left')
    ax.text(2, 3.2, '2. raw_data → 数据清洗 → 分区处理 → pg_data & other_data', fontsize=10, ha='left')
    ax.text(2, 2.9, '3. 用户编辑 → edited_data → update_raw_data_from_edits → 状态更新', fontsize=10, ha='left')
    ax.text(2, 2.6, '4. PDF生成 → 数据准备 → 地图生成 → 报告输出 → 下载链接', fontsize=10, ha='left')
    ax.text(2, 2.3, '5. 错误处理 → 日志记录 → 用户提示 → 状态恢复', fontsize=10, ha='left')
    
    # 绘制连接线
    draw_precipitation_connections(ax)
    
    plt.tight_layout()
    plt.savefig('降水查询系统流程图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✅ 降水查询系统流程图已生成: 降水查询系统流程图.png")

def draw_box(ax, x, y, width, height, text, color):
    """绘制带文本的方框"""
    box = FancyBboxPatch((x, y), width, height,
                        boxstyle="round,pad=0.1",
                        facecolor=color,
                        edgecolor='black',
                        linewidth=1.2)
    ax.add_patch(box)
    
    # 添加文本
    ax.text(x + width/2, y + height/2, text,
            ha='center', va='center',
            fontsize=9, fontweight='bold',
            wrap=True)

def draw_precipitation_connections(ax):
    """绘制降水系统的连接线"""
    # 系统初始化到用户界面
    ax.arrow(12, 31, 0, -2, head_width=0.3, head_length=0.2, fc='blue', ec='blue', linewidth=2)
    
    # 用户界面到API调用
    ax.arrow(12, 27, 0, -1.5, head_width=0.3, head_length=0.2, fc='green', ec='green', linewidth=2)
    
    # API调用到数据处理
    ax.arrow(12, 23.5, 0, -2, head_width=0.3, head_length=0.2, fc='orange', ec='orange', linewidth=2)
    
    # 数据处理到数据展示
    ax.arrow(12, 19.5, 0, -1.5, head_width=0.3, head_length=0.2, fc='purple', ec='purple', linewidth=2)
    
    # 数据展示到数据编辑
    ax.arrow(12, 16, 0, -1.5, head_width=0.3, head_length=0.2, fc='red', ec='red', linewidth=2)
    
    # 数据编辑到报告生成
    ax.arrow(12, 12.5, 0, -1, head_width=0.3, head_length=0.2, fc='cyan', ec='cyan', linewidth=2)
    
    # 报告生成到系统保障
    ax.arrow(12, 8.5, 0, -1.5, head_width=0.3, head_length=0.2, fc='brown', ec='brown', linewidth=2)
    
    # 侧向连接线 - 表示数据流
    # 表格视图到地图视图
    ax.arrow(8.5, 17.4, 0.5, 0, head_width=0.2, head_length=0.2, fc='gray', ec='gray')
    
    # 平谷数据到其他区域数据
    ax.arrow(7, 13.9, 0.5, 0, head_width=0.2, head_length=0.2, fc='gray', ec='gray')
    
    # 错误处理反馈线 (虚线)
    ax.plot([22, 22, 1, 1], [6, 32, 32, 27], 'r--', linewidth=1.5, alpha=0.7)
    ax.text(0.5, 29, '错误\n反馈', fontsize=8, ha='center', color='red', rotation=90)

if __name__ == "__main__":
    create_precipitation_flowchart()
