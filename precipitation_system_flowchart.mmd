flowchart TD
    A[🚀 系统启动] --> B[📦 导入模块和库]
    B --> C[⚙️ 页面配置设置]
    C --> D[🎨 CSS样式加载]
    D --> E[📊 Session State初始化]
    
    E --> F[🏠 主页面渲染]
    F --> G[📋 侧边栏查询条件]
    
    G --> H{用户是否点击查询?}
    H -->|否| I[⏳ 等待用户输入]
    I --> H
    
    H -->|是| J[📅 获取查询参数]
    J --> K[🔄 重置PDF状态]
    K --> L[🌐 调用Station API]
    
    L --> M{API调用成功?}
    M -->|否| N[❌ 显示错误信息]
    N --> I
    
    M -->|是| O[📊 获取原始数据]
    O --> P[🧹 数据清洗和类型转换]
    P --> Q[✅ 数据验证]
    Q --> R[📍 数据分区处理]
    
    R --> S[🏘️ 平谷区数据]
    R --> T[🌍 其他区域数据]
    
    S --> U[📈 计算统计指标]
    T --> U
    U --> V[💳 显示统计卡片]
    
    V --> W[📋 表格视图渲染]
    W --> X[🗺️ 地图视图准备]
    
    X --> Y{用户选择视图类型}
    Y -->|表格视图| Z[📊 可编辑数据表格]
    Y -->|地图视图| AA[🗺️ 降水分布地图]
    
    Z --> BB{用户是否编辑数据?}
    BB -->|否| CC[👀 数据浏览模式]
    BB -->|是| DD[✏️ 数据编辑模式]
    
    DD --> EE{用户点击保存?}
    EE -->|否| DD
    EE -->|是| FF[🔄 更新原始数据]
    FF --> GG[✅ 数据验证和类型转换]
    GG --> HH[🔄 重新生成显示数据]
    HH --> II[✅ 显示保存成功]
    II --> JJ[🔄 页面重新渲染]
    
    CC --> KK{用户点击生成PDF?}
    JJ --> KK
    AA --> KK
    
    KK -->|否| LL[⏳ 等待用户操作]
    LL --> Y
    
    KK -->|是| MM[📋 数据完整性检查]
    MM --> NN{数据是否完整?}
    NN -->|否| OO[⚠️ 显示警告信息]
    OO --> LL
    
    NN -->|是| PP[🗺️ 地图缓存检查]
    PP --> QQ{地图缓存存在?}
    QQ -->|是| RR[✅ 使用缓存地图]
    QQ -->|否| SS[🎨 生成新地图]
    
    SS --> TT{地图生成成功?}
    TT -->|否| UU[⚠️ 无地图模式]
    TT -->|是| VV[💾 缓存地图数据]
    VV --> RR
    
    RR --> WW[📊 准备PDF数据]
    UU --> WW
    WW --> XX[🔍 获取最大小时雨量]
    XX --> YY[📄 调用PDF生成模块]
    
    YY --> ZZ{PDF生成成功?}
    ZZ -->|否| AAA[❌ 显示生成失败]
    ZZ -->|是| BBB[✅ PDF生成完成]
    
    BBB --> CCC[📥 提供下载链接]
    CCC --> DDD[👁️ PDF预览功能]
    
    AAA --> LL
    DDD --> LL
    
    %% 样式定义
    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000
    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000
    classDef userAction fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    classDef dataProcess fill:#fce4ec,stroke:#c2185b,stroke-width:2px,color:#000
    classDef error fill:#ffebee,stroke:#d32f2f,stroke-width:2px,color:#000
    classDef success fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000
    
    %% 应用样式
    class A,LL startEnd
    class B,C,D,E,F,G,J,K,L,O,P,Q,R,S,T,U,V,W,X,FF,GG,HH,MM,PP,WW,XX,YY process
    class H,M,Y,BB,EE,KK,NN,QQ,TT,ZZ decision
    class I,CC,DD userAction
    class Z,AA,RR,SS,VV dataProcess
    class N,AAA,OO,UU error
    class II,BBB,CCC,DDD success