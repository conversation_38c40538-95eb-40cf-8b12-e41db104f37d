import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import datetime
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.graph_objects as go
from station_api import main as run_station_api, get_pinggu_max_hourly_rain
from station_api.data import StationData  # 导入 StationData 类
import asyncio
import os # Import os module
from rainfall_map_generator import plotting 
from report import generate_pdf
import PyPDF2 # 添加 PyPDF2 导入
import io # Import io for BytesIO objects
import requests # 导入requests库用于HTTP请求
import json # 导入json库用于处理JSON数据
import base64 # Import base64 for PDF preview
import traceback # 导入 traceback 用于错误追踪
import logging # 导入 logging
import shutil

# 设置日志
logger = logging.getLogger(__name__)

def get_max_hourly_data(rain_data):
    """
    获取平谷区最大小时雨量数据
    
    Args:
        rain_data: 包含雨量数据的DataFrame (使用已处理的pg_data)
        
    Returns:
        Dict: 包含最大小时雨量信息的字典
    """
    try:
        # 简化逻辑：直接使用现有的pg_data，因为它已经包含了平谷区的数据和'近一小时雨量'列
        # 这样避免了时间列丢失的问题
        pg_data = st.session_state.pg_data.copy() if 'pg_data' in st.session_state else pd.DataFrame()
        
        print(f"平谷区站点数: {len(pg_data)}")

        if pg_data.empty:
            logger.warning("平谷区数据为空")
            return {
                'success': False,
                'max_rainfall': 0,
                'station_name': "无数据",
                'station_id': None,
                'time': "无数据",
                'message': '平谷区无有效雨量数据'
            }
        
        # 检查是否有'近一小时雨量'列
        if '近一小时雨量' not in pg_data.columns:
            print(f"平谷区数据列名: {pg_data.columns.tolist()}")
            return {
                'success': False,
                'max_rainfall': 0,
                'station_name': "数据格式错误",
                'station_id': None,
                'time': "数据格式错误",
                'message': '未找到近一小时雨量数据列'
            }
        
        try:
            # 确保'近一小时雨量'列是数值类型
            pg_data['近一小时雨量'] = pd.to_numeric(pg_data['近一小时雨量'], errors='coerce')
            
            # 过滤掉无效数据
            valid_data = pg_data.dropna(subset=['近一小时雨量'])
            valid_data = valid_data[valid_data['近一小时雨量'] > 0]
            
            if valid_data.empty:
                return {
                    'success': False,
                    'max_rainfall': 0,
                    'station_name': "无有效数据",
                    'station_id': None,
                    'time': "无有效数据",
                    'message': '所有平谷区站点近一小时雨量均为0或无效'
                }
            
            # 找到最大小时雨量
            max_row = valid_data.loc[valid_data['近一小时雨量'].idxmax()]
            max_rainfall = max_row['近一小时雨量']
            station_name = max_row['站名']
            station_id = max_row.get('区站号', max_row.name)
            
            # 获取查询时间信息
            query_params = st.session_state.get('query_params', {})
            end_time = query_params.get('end_datetime')
            time_str = end_time.strftime('%Y-%m-%d %H:%M:%S') if end_time else '查询结束时间'
            
            print(f"平谷区最大小时雨量: {max_rainfall} mm，站点: {station_name}，时间段: {time_str}")
            
            return {
                'success': True,
                'max_rainfall': max_rainfall,
                'station_name': station_name,
                'station_id': station_id,
                'time': time_str,
                'message': '成功获取平谷区最大小时雨量'
            }
            
        except Exception as calc_error:
            logger.error(f"计算最大小时雨量时出错: {calc_error}")
            return {
                'success': False,
                'max_rainfall': 0,
                'station_name': "计算错误",
                'station_id': None,
                'time': "计算错误",
                'message': f"计算失败: {str(calc_error)}"
            }

    except Exception as e:
        logger.error(f"获取平谷区最大小时雨量时发生严重错误: {str(e)}")
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return {
            'success': False,
            'max_rainfall': 0,
            'station_name': "获取失败",
            'station_id': None,
            'time': "获取失败",
            'message': f"获取失败: 发生了意外错误 ({type(e).__name__})"
        }

# Set custom theme and styling
st.set_page_config(
    page_title="降水查询系统",
    page_icon="🌧️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    /* Main theme colors */
    :root {
        --primary-color: #3498db;
        --secondary-color: #2980b9;
        --accent-color: #1abc9c;
        --background-color: #f8f9fa;
        --text-color: #2c3e50;
        --border-color: #e0e0e0;
        --hover-color: #f1f9ff;
        --edit-color: #fffde7;
    }
    
    /* Header styling */
    .main-header {
        color: var(--primary-color);
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-align: center;
        padding: 1rem 0;
        border-bottom: 2px solid var(--primary-color);
    }
    
    .sub-header {
        color: var(--text-color);
        font-size: 1.2rem;
        font-weight: 400;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    /* Card styling */
    .metric-card {
        background-color: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        text-align: center;
        transition: transform 0.3s ease;
    }
    
    .metric-card:hover {
        transform: translateY(-5px);
    }
    
    .metric-value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-color);
    }
    
    .metric-label {
        font-size: 1rem;
        color: var(--text-color);
        margin-top: 0.5rem;
    }
    
    /* Sidebar styling */
    .sidebar .sidebar-content {
        background-color: #f1f3f6;
    }
    
    /* Button styling */
    .stButton>button {
        background-color: var(--primary-color);
        color: white;
        border-radius: 5px;
        padding: 0.5rem 1rem;
        font-weight: 600;
        border: none;
        width: 100%;
        transition: all 0.3s ease;
    }
    
    .stButton>button:hover {
        background-color: var(--secondary-color);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    /* Download button styling */
    [data-testid="stButton"][key="download_pdf_button"] button {
        background-color: #2ecc71;
        color: white;
    }
    
    [data-testid="stButton"][key="download_pdf_button"] button:hover {
        background-color: #27ae60;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    /* Download link styling */
    [data-testid="stDownloadButton"] button {
        background-color: #27ae60;
        color: white;
        border-radius: 5px;
        padding: 0.5rem 1rem;
        font-weight: 600;
        border: none;
        width: 100%;
        transition: all 0.3s ease;
    }
    
    [data-testid="stDownloadButton"] button:hover {
        background-color: #219653;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    /* Tab styling */
    .stTabs [data-baseweb="tab-list"] {
        gap: 1rem;
    }
    
    .stTabs [data-baseweb="tab"] {
        background-color: #f1f3f6;
        border-radius: 5px 5px 0 0;
        padding: 0.5rem 1rem;
        font-weight: 600;
    }
    
    .stTabs [aria-selected="true"] {
        background-color: var(--primary-color) !important;
        color: white !important;
    }
    
    /* Inner tab styling - for region tabs */
    .region-tabs .stTabs [data-baseweb="tab-list"] {
        gap: 0.5rem;
    }
    
    .region-tabs .stTabs [data-baseweb="tab"] {
        background-color: #f8f9fa;
        border-radius: 5px 5px 0 0;
        padding: 0.4rem 0.8rem;
        font-weight: 500;
        font-size: 0.9rem;
    }
    
    .region-tabs .stTabs [aria-selected="true"] {
        background-color: #2980b9 !important;
        color: white !important;
    }
    
    /* Footer styling */
    .footer {
        margin-top: 3rem;
        padding-top: 1rem;
        border-top: 1px solid #e0e0e0;
        color: #666;
        font-size: 0.9rem;
    }
    
    /* Info box styling */
    .info-box {
        background-color: #e8f4fd;
        border-left: 5px solid var(--primary-color);
        padding: 1rem;
        border-radius: 0 5px 5px 0;
    }
    
    /* Enhanced Data Editor Styling */
    .stDataEditor {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
        border: 1px solid var(--border-color);
    }
    
    /* Table header styling */
    .stDataEditor [data-testid="stDataEditorTableContainer"] th {
        background-color: var(--primary-color) !important;
        color: white !important;
        font-weight: 600 !important;
        padding: 12px 16px !important;
        text-align: center !important;
        border-bottom: 2px solid #2980b9 !important;
    }
    
    /* Table cell styling */
    .stDataEditor [data-testid="stDataEditorTableContainer"] td {
        padding: 10px 16px !important;
        border-bottom: 1px solid var(--border-color) !important;
        font-size: 14px !important;
    }
    
    /* Alternating row colors */
    .stDataEditor [data-testid="stDataEditorTableContainer"] tr:nth-child(even) {
        background-color: #f8f9fa !important;
    }
    
    /* Row hover effect */
    .stDataEditor [data-testid="stDataEditorTableContainer"] tr:hover {
        background-color: var(--hover-color) !important;
    }
    
    /* Editable cell styling */
    .stDataEditor [data-testid="stDataEditorTableContainer"] td[contenteditable="true"] {
        background-color: var(--edit-color) !important;
        border: 1px dashed #ffc107 !important;
        transition: all 0.2s ease;
    }
    
    .stDataEditor [data-testid="stDataEditorTableContainer"] td[contenteditable="true"]:hover {
        background-color: #fff8e1 !important;
        border: 1px solid #ffc107 !important;
    }
    
    /* Focus on editable cell */
    .stDataEditor [data-testid="stDataEditorTableContainer"] td[contenteditable="true"]:focus {
        background-color: white !important;
        border: 2px solid var(--primary-color) !important;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2) !important;
        outline: none !important;
    }
    
    /* Table container styling */
    .stDataEditor [data-testid="stDataEditorTableContainer"] {
        border-radius: 10px !important;
        overflow: hidden !important;
    }
    
    /* Table wrapper */
    .table-wrapper {
        background-color: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        margin-bottom: 30px;
    }
    
    /* Table title */
    .table-title {
        color: var(--primary-color);
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 15px;
        border-bottom: 2px solid var(--primary-color);
        padding-bottom: 10px;
    }
    
    /* Table description */
    .table-description {
        background-color: #e8f4fd;
        padding: 10px 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        border-left: 4px solid var(--primary-color);
        font-size: 0.9rem;
    }
    
    /* Edit indicator */
    .edit-indicator {
        display: inline-block;
        background-color: #fffde7;
        border: 1px dashed #ffc107;
        padding: 2px 8px;
        border-radius: 4px;
        margin-right: 8px;
        font-size: 0.8rem;
    }
    
    /* Region indicator */
    .region-indicator {
        display: inline-block;
        background-color: #e3f2fd;
        border: 1px solid #2196f3;
        padding: 2px 8px;
        border-radius: 4px;
        margin-right: 8px;
        font-size: 0.8rem;
        color: #0d47a1;
    }
    
    .other-region-indicator {
        display: inline-block;
        background-color: #f1f8e9;
        border: 1px solid #8bc34a;
        padding: 2px 8px;
        border-radius: 4px;
        margin-right: 8px;
        font-size: 0.8rem;
        color: #33691e;
    }
    
    /* Success message styling */
    .stSuccess {
        background-color: #e8f5e9 !important;
        border-left: 4px solid #4caf50 !important;
        padding: 16px !important;
        border-radius: 4px !important;
        color: #2e7d32 !important;
        margin: 20px 0 !important;
    }
    
    /* Empty table message */
    .empty-table-message {
        background-color: #f5f5f5;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        color: #757575;
        margin: 20px 0;
        border: 1px dashed #bdbdbd;
    }
</style>
""", unsafe_allow_html=True)

# Application title with custom styling
st.markdown('<h1 class="main-header">🌧️ 降水查询系统</h1>', unsafe_allow_html=True)
st.markdown('<p class="sub-header">查询特定时间段内的降水数据，分析降水趋势和分布</p>', unsafe_allow_html=True)

# Initialize session state variables
if 'query_params' not in st.session_state:
    st.session_state.query_params = {
        'start_datetime': (datetime.datetime.now() - datetime.timedelta(hours=3)).replace(minute=0, second=0, microsecond=0),
        'end_datetime': datetime.datetime.now().replace(minute=0, second=0, microsecond=0),
    }

if 'has_queried' not in st.session_state:
    st.session_state.has_queried = False

# 预警相关的状态变量已移除
    
# 添加防止重复执行操作的标志
if 'is_updating_data' not in st.session_state:
    st.session_state.is_updating_data = False
    
# 初始化PDF相关的session state
if 'pdf_generated' not in st.session_state:
    st.session_state.pdf_generated = False
    
if 'pdf_filename' not in st.session_state:
    st.session_state.pdf_filename = None

if 'show_preview' not in st.session_state:
    st.session_state.show_preview = False
    
if 'pdf_content' not in st.session_state:
    st.session_state.pdf_content = None
    
if 'preview_error' not in st.session_state:
    st.session_state.preview_error = None

# 获取预警状态的函数已移除

# Create sidebar filters with improved styling
with st.sidebar:
    st.markdown('<h2 style="color: #3498db; border-bottom: 2px solid #3498db; padding-bottom: 10px;">查询条件</h2>', unsafe_allow_html=True)
    
    # Date range selector
    st.markdown('<p style="font-weight: 600; margin-bottom: 5px;">日期范围</p>', unsafe_allow_html=True)
    col1, col2 = st.columns(2)
    with col1:
        start_date = st.date_input(
            "开始日期",
            st.session_state.query_params['start_datetime'].date()
        )
    with col2:
        end_date = st.date_input(
            "结束日期",
            st.session_state.query_params['end_datetime'].date()
        )

    # Time range selector
    st.markdown('<p style="font-weight: 600; margin-bottom: 5px; margin-top: 15px;">时间范围</p>', unsafe_allow_html=True)
    col1, col2 = st.columns(2)
    with col1:
        start_time = st.time_input("开始时间", st.session_state.query_params['start_datetime'].time())
    with col2:
        end_time = st.time_input("结束时间", st.session_state.query_params['end_datetime'].time())

    # Combine date and time
    start_datetime = datetime.datetime.combine(start_date, start_time)
    end_datetime = datetime.datetime.combine(end_date, end_time)

    st.markdown('<div style="height: 20px;"></div>', unsafe_allow_html=True)
    
    # Query button
    query_button = st.button("查询数据", type="primary")
    
    # Add app info in sidebar
    st.markdown('<div style="height: 30px;"></div>', unsafe_allow_html=True)
    st.markdown("""
    <div style="background-color: #e8f4fd; padding: 15px; border-radius: 5px; border-left: 5px solid #3498db;">
        <h4 style="color: #3498db; margin-top: 0;">关于本系统</h4>
        <p style="font-size: 0.9rem; margin-bottom: 0;">
            本系统提供平谷及周边地区降水数据查询和可视化分析功能，帮助您了解降水趋势和分布情况。表格数据支持直接编辑，修改后的数据将自动更新到图表中。
        </p>
    </div>
    """, unsafe_allow_html=True)

def update_raw_data_from_edits(edited_pg, edited_other):
    """
    根据两个数据编辑器的编辑结果更新 st.session_state.raw_data。
    标记为 use_data=False 的行将被从原始数据中移除。

    Args:
        edited_pg (pd.DataFrame): 从 'pg_data_editor' 返回的编辑后的 DataFrame。
        edited_other (pd.DataFrame): 从 'other_data_editor' 返回的编辑后的 DataFrame。

    Returns:
        bool: 如果更新成功则返回 True，否则返回 False。
    """
    print("\n--- Entering update_raw_data_from_edits ---")
    try:
 
        if not isinstance(edited_pg, pd.DataFrame) or not isinstance(edited_other, pd.DataFrame):
            st.error("输入数据类型错误，需要 Pandas DataFrame。")
            return False


        cols_to_update = ['总降雨量', '近一小时雨量','use_data']
        
        # 处理平谷数据更新
        if not edited_pg.empty:
            update_payload_pg = edited_pg[cols_to_update]
            st.session_state.raw_data.update(update_payload_pg)
         

        # 处理其他区域数据更新
        if not edited_other.empty:
            update_payload_other = edited_other[cols_to_update]
          
            st.session_state.raw_data.update(update_payload_other)


        # 不要永久删除数据，保留原始数据以防意外丢失
        # st.session_state.raw_data = st.session_state.raw_data[st.session_state.raw_data['use_data']]
       
        return True
        
    except Exception as e:
        st.error(f"更新数据时发生错误: {e}")
        print(f"Error encountered in update_raw_data_from_edits: {e}")
        print("--- Exiting update_raw_data_from_edits with error ---")
        return False

# Update query parameters and filter data when query button is clicked
if query_button:
    # 重置PDF生成状态
    st.session_state.pdf_generated = False
    st.session_state.pdf_filename = None
    
    st.session_state.query_params = {
        'start_datetime': start_datetime,
        'end_datetime': end_datetime,
    }
    
    # 预警状态获取功能已移除
    
    # Filter data based on query parameters
    st.session_state.raw_data = asyncio.run(run_station_api(
        start_time=start_datetime,
        end_time=end_datetime
    ))
    # Reset index to ensure uniqueness before further operations
    # st.session_state.raw_data = st.session_state.raw_data.reset_index(drop=True) # 保留原始索引
    
    # --- Data Cleaning and Type Conversion --- START
    # Ensure correct data types, especially for string columns causing Arrow issues
    if 'raw_data' in st.session_state and not st.session_state.raw_data.empty:
        try:
            string_cols = ['站名', '乡镇', '区县'] # Add other object cols if needed
            for col in string_cols:
                if col in st.session_state.raw_data.columns:
                    # Convert to Pandas' dedicated string type, handling potential errors
                    st.session_state.raw_data[col] = st.session_state.raw_data[col].astype(str).fillna('')

            numeric_cols = ['总降雨量', '近一小时雨量']
            for col in numeric_cols:
                 if col in st.session_state.raw_data.columns:
                      st.session_state.raw_data[col] = pd.to_numeric(st.session_state.raw_data[col], errors='coerce')
            
            # Ensure 'problem' column is boolean
            if 'problem' in st.session_state.raw_data.columns:
                 # Attempt to convert, coercing errors to False (adjust default if needed)
                 st.session_state.raw_data['problem'] = st.session_state.raw_data['problem'].astype(bool, errors='ignore') 
                 # Fill potential NaNs introduced by coercion if necessary
                 # st.session_state.raw_data['problem'] = st.session_state.raw_data['problem'].fillna(False) # Example: fill NaNs with False

        except Exception as e:
            st.error(f"数据类型转换时出错: {e}")
    # --- Data Cleaning and Type Conversion --- END

    # 检查原始索引是否有重复项
    if st.session_state.raw_data.index.duplicated().any():
        st.warning("警告：从 API 获取的原始数据索引中发现重复项。这可能导致更新时出现问题。")
        # 可以在这里打印重复的索引值以供调试
        st.write("重复的索引:", st.session_state.raw_data.index[st.session_state.raw_data.index.duplicated()])
    else:
        st.info("原始数据索引检查通过，未发现重复项。")

    print(st.session_state.raw_data)

    st.session_state.raw_data['总降雨量'] = pd.to_numeric(st.session_state.raw_data['总降雨量'], errors='coerce')
    st.session_state.raw_data['近一小时雨量'] = pd.to_numeric(st.session_state.raw_data['近一小时雨量'], errors='coerce')
   
    st.session_state.raw_data['problem'] = st.session_state.raw_data['problem'].astype(bool)
    st.session_state.raw_data['use_data'] = ~st.session_state.raw_data['problem']
    
    st.session_state.raw_data = st.session_state.raw_data.reset_index()
    
    # 过滤显示数据时只显示 use_data=True 的数据，但保留原始数据
    filtered_data_for_display = st.session_state.raw_data[st.session_state.raw_data['use_data']]
    st.session_state.pg_data = filtered_data_for_display[filtered_data_for_display['区县'] == '平谷'][['区站号','站名','乡镇','总降雨量','近一小时雨量','problem','use_data']]
    st.session_state.other_data = filtered_data_for_display[filtered_data_for_display['区县'] != '平谷'][['区站号','站名','区县','总降雨量','近一小时雨量','problem','use_data']]
   
 
    st.session_state.has_queried = True

# Main content
if st.session_state.has_queried:
    # If there is data, show results
    if not st.session_state.pg_data.empty:
        # Create three metric cards with improved styling
        st.markdown('<div style="padding: 10px;"></div>', unsafe_allow_html=True)
        
        # 预警状态显示和修改功能已移除
        
        # Calculate statistics
        total_stations = len(st.session_state.pg_data)
    
        max_precipitation = st.session_state.pg_data['总降雨量'].max()
        max_name = st.session_state.pg_data.loc[st.session_state.pg_data['总降雨量'].idxmax(), '站名']
        max_hourly = st.session_state.pg_data['近一小时雨量'].max()
        max_hourly_name = st.session_state.pg_data.loc[st.session_state.pg_data['近一小时雨量'].idxmax(), '站名']
        avg_precipitation = st.session_state.pg_data['总降雨量'].mean()
        
        # 使用新的API获取平谷区最大小时雨量数据
        try:
            start_dt = st.session_state.query_params.get('start_datetime')
            end_dt = st.session_state.query_params.get('end_datetime') 
            max_hourly_result = asyncio.run(get_pinggu_max_hourly_rain(start_dt, end_dt))
            
            if max_hourly_result is not None:
                max_rainfall, station_name, max_time = max_hourly_result
                api_display_value = f"{station_name} {max_rainfall:.1f} mm"
                api_display_time = max_time
                api_success = True
            else:
                api_display_value = "查询失败"
                api_display_time = ""
                api_success = False
        except Exception as e:
            logger.error(f"调用新API获取最大小时雨量失败: {e}")
            api_display_value = "API调用失败"
            api_display_time = ""
            api_success = False
        
        col1, col2, col3, col4, col5 = st.columns(5)
        
        with col1:
            st.markdown(f"""
            <div class="metric-card">
                <div class="metric-value">{total_stations} 个</div>
                <div class="metric-label">监测站点数</div>
            </div>
            """, unsafe_allow_html=True)
        
        with col2:
            st.markdown(f"""
            <div class="metric-card">
                <div class="metric-value">{avg_precipitation:.1f} mm</div>
                <div class="metric-label">平均降水量</div>
            </div>
            """, unsafe_allow_html=True)
        
        with col3:
            st.markdown(f"""
            <div class="metric-card">
                <div class="metric-value">{max_name}{max_precipitation:.1f} mm</div>
                <div class="metric-label">最大降水量</div>
            </div>
            """, unsafe_allow_html=True)

        with col4:
            st.markdown(f"""
            <div class="metric-card">
                <div class="metric-value">{max_hourly_name}{max_hourly:.1f} mm</div>
                <div class="metric-label">最近一小时最大降水量</div>
            </div>
            """, unsafe_allow_html=True)

        with col5:
            # 显示平谷区最大小时雨量（使用新API）
            st.markdown(f"""
            <div class="metric-card">
                <div class="metric-value" style="font-size: 1.5rem;">{api_display_value}</div>
                <div class="metric-label">平谷区最大小时雨量 (新API)</div>
                {f'<div style="font-size: 0.8rem; color: #666; margin-top: 5px;">{api_display_time}</div>' if api_display_time else ''}
            </div>
            """, unsafe_allow_html=True)
        
      
        
        # 添加显示全部数据的选项
        col_show_all, col_spacer = st.columns([3, 7])
        with col_show_all:
            show_all_data = st.checkbox("显示所有数据(包括被标记为不使用的)", value=False, help="选中后将显示所有数据，包括被标记为'不使用'的站点数据")
        
        # Create tabs with improved styling
        tab1, tab2 = st.tabs(["📊 表格视图", "🗺️ 地图视图"])
        
        with tab1:
            # Display editable table with enhanced styling
            # st.markdown('<div class="table-wrapper">', unsafe_allow_html=True)
            st.markdown('<h2 class="table-title">站点降水量统计</h2>', unsafe_allow_html=True)
            
            st.markdown("""
            <div class="table-description">
                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                    <span class="edit-indicator">可编辑</span>
                    <span>表格中的"总降水量(mm)"和"最大时降水量(mm)"列可以直接编辑</span>
                </div>
                <p style="margin: 0;">修改数据后，系统将自动更新所有图表和统计信息。</p>
            </div>
            """, unsafe_allow_html=True)
            
        
                
                # Always create region tabs regardless of region selection
            region_tab1, region_tab2 = st.tabs(["📍 本区", "🌏 其他区域"])
            
            with region_tab1:
                
                st.markdown(f"""
                <div style="margin-bottom: 15px;">
                    <span class="region-indicator">平谷地区</span>
                </div>
                """, unsafe_allow_html=True)
                
              
                
                # 根据用户选择决定显示的数据
                if show_all_data:
                    # 显示所有数据，包括被标记为不使用的
                    display_pg_data = st.session_state.raw_data[st.session_state.raw_data['区县'] == '平谷'][['区站号','站名','乡镇','总降雨量','近一小时雨量','problem','use_data']]
                    st.info("🔍 当前显示所有平谷区数据，包括被标记为'不使用'的站点数据。")
                else:
                    # 只显示使用的数据
                    display_pg_data = st.session_state.pg_data
                
                edited_pg = st.data_editor(
                    display_pg_data,
                    hide_index=True, # Pass the DataFrame with reset index
                    key="pg_data_editor", # 分配唯一的 key
                    column_config={
                        "区站号": st.column_config.TextColumn(
                            "区站号", # Display the original index
                            disabled=True
                        ),
                        "站名": st.column_config.TextColumn(
                            "站名",
                            disabled=True,
                            help="站点名称"
                        ),
                        "乡镇": st.column_config.TextColumn(
                            "乡镇",
                            disabled=True,
                            help="站点所在乡镇"
                        ),

                        "总降雨量": st.column_config.NumberColumn(
                            "总降水量(mm)",
                            format="%.1f mm",
                            min_value=0,
                            help="站点累计降水量，可编辑",
                            max_value=1000,
                        ),
                        "近一小时雨量": st.column_config.NumberColumn(
                            "近一小时雨量(mm)",
                            format="%.1f mm",
                            min_value=0,
                            help="站点单小时最大降水量，可编辑",
                            max_value=200,
                        ),
                        "problem": st.column_config.CheckboxColumn(
                            "问题数据?",
                            help="标记为问题的数据行",
                            width="small",
                            disabled=True,
                        ),
                        "use_data": st.column_config.CheckboxColumn(
                            "使用此数据?",
                            help="选择是否使用此行数据",
                        ),
                    },
                    use_container_width=True,
                    num_rows="fixed",
                )
           
            
            with region_tab2:
                
                # 当选择"全部"区域时，其他区域标签显示分区统计信息
                st.markdown(f"""
                <div style="margin-bottom: 15px;">
                    <span class="other-region-indicator">其他地区</span>
                </div>
                """, unsafe_allow_html=True)
                
             
                # 根据用户选择决定显示的数据
                if show_all_data:
                    # 显示所有数据，包括被标记为不使用的
                    display_other_data = st.session_state.raw_data[st.session_state.raw_data['区县'] != '平谷'][['区站号','站名','区县','总降雨量','近一小时雨量','problem','use_data']]
                    st.info("🔍 当前显示所有其他地区数据，包括被标记为'不使用'的站点数据。")
                else:
                    # 只显示使用的数据
                    display_other_data = st.session_state.other_data
                
                edited_other = st.data_editor(
                    display_other_data, # Pass the DataFrame with reset index
                    key="other_data_editor", # 分配唯一的 key
                    column_config={
                        "区站号": st.column_config.TextColumn(
                            "区站号", # Display the original index
                            disabled=True
                        ),
                        "站名": st.column_config.TextColumn(
                            "站名",
                            disabled=True,
                            help="站点名称"
                        ),
                        "区县": st.column_config.TextColumn(
                            "区县",
                            disabled=True,
                            help="站点所在乡镇"
                        ),

                        "总降水量": st.column_config.NumberColumn(
                            "总降水量(mm)",
                            format="%.1f mm",
                            min_value=0,
                            help="站点累计降水量，可编辑",
                            max_value=1000,
                        ),
                        "近一小时雨量": st.column_config.NumberColumn(
                            "近一小时雨量(mm)",
                            format="%.1f mm",
                            min_value=0,
                            help="站点单小时最大降水量，可编辑",
                            max_value=200,
                        ),
                        "problem": st.column_config.CheckboxColumn(
                            "问题数据?",
                            disabled=True,
                            help="标记为问题的数据行",
                            width="small",
                        ),
                        "use_data": st.column_config.CheckboxColumn(
                            "使用此数据?",
                            help="选择是否使用此行数据",
                        ),
                    },
                    use_container_width=True,
                    num_rows="fixed",
                )
        
        # 添加保存按钮
        col1, col2 = st.columns(2)
        with col1:
            if st.button("保存修改", key="save_changes_button"):
                # 使用标志防止重复执行
                if not st.session_state.is_updating_data:
                     try:
                         st.session_state.is_updating_data = True
                         
                         

                         update_success = update_raw_data_from_edits(edited_pg, edited_other)
                         if update_success:
                             st.success("数据已成功更新并保存！图表和统计信息将使用新数据重新计算。")
                             
                             # --- Re-apply Type Conversion AFTER update_raw_data_from_edits --- START
                             # Crucial step: Ensure types are correct again after potential modification by update/drop
                             try:
                                 if 'raw_data' in st.session_state and not st.session_state.raw_data.empty:
                                     string_cols = ['站名', '乡镇', '区县'] 
                                     for col in string_cols:
                                         if col in st.session_state.raw_data.columns:
                                             st.session_state.raw_data[col] = st.session_state.raw_data[col].astype(str).fillna('')

                                     numeric_cols = ['总降雨量', '近一小时雨量']
                                     for col in numeric_cols:
                                          if col in st.session_state.raw_data.columns:
                                               st.session_state.raw_data[col] = pd.to_numeric(st.session_state.raw_data[col], errors='coerce')
                                    
                                     # Ensure 'problem' column is boolean
                                     if 'problem' in st.session_state.raw_data.columns:
                                          st.session_state.raw_data['problem'] = st.session_state.raw_data['problem'].astype(bool, errors='ignore')
                                          st.session_state.raw_data['problem'] = st.session_state.raw_data['problem'].fillna(False) # Ensure no NaNs remain
                                 else:
                                     st.warning("raw_data 为空或不存在，跳过保存后的类型转换。")

                             except Exception as e:
                                 st.error(f"保存修改后进行类型转换时出错: {e}")
                             # --- Re-apply Type Conversion AFTER update_raw_data_from_edits --- END

                             # --- Regenerate pg_data and other_data from updated raw_data --- START
                             # Simpler regeneration based only on the updated raw_data
                             try:
                                 # Ensure raw_data still exists and is a DataFrame
                                 if 'raw_data' in st.session_state and isinstance(st.session_state.raw_data, pd.DataFrame):
                                    # 过滤显示数据时只显示 use_data=True 的数据，但保留原始数据
                                    filtered_data = st.session_state.raw_data[st.session_state.raw_data['use_data']]
                                    st.session_state.pg_data = filtered_data[filtered_data['区县'] == '平谷'][['区站号','站名','乡镇','总降雨量','近一小时雨量','problem','use_data']]
                                    st.session_state.other_data = filtered_data[filtered_data['区县'] != '平谷'][['区站号','站名','区县','总降雨量','近一小时雨量','problem','use_data']]
    
                                     # Re-filter Pinggu data from the potentially modified raw_data
                                    
                                 else:
                                     st.warning("无法重新生成显示数据，因为 'raw_data' 不可用或格式不正确。")
                                     # Initialize empty DataFrames to prevent errors downstream
                                     st.session_state.pg_data = pd.DataFrame(columns=['站名','乡镇','总降雨量','近一小时雨量','problem', 'use_data'])
                                     st.session_state.other_data = pd.DataFrame(columns=['站名','区县','总降雨量','近一小时雨量','problem', 'use_data'])

                             except KeyError as e:
                                 st.error(f"重新生成编辑数据时出错：缺少列 {e}。请检查 `raw_data` 更新后的结构。")
                                 # Initialize empty DataFrames to prevent errors downstream
                                 st.session_state.pg_data = pd.DataFrame(columns=['站名','乡镇','总降雨量','近一小时雨量','problem', 'use_data'])
                                 st.session_state.other_data = pd.DataFrame(columns=['站名','区县','总降雨量','近一小时雨量','problem', 'use_data'])
                             except Exception as e:
                                 st.error(f"重新生成编辑数据时发生未知错误: {e}")
                                 # Initialize empty DataFrames to prevent errors downstream
                                 st.session_state.pg_data = pd.DataFrame(columns=['站名','乡镇','总降雨量','近一小时雨量','problem', 'use_data'])
                                 st.session_state.other_data = pd.DataFrame(columns=['站名','区县','总降雨量','近一小时雨量','problem', 'use_data'])
                             # --- Regenerate pg_data and other_data --- END

                     finally:
                         # 重置标志，无论成功与否
                         st.session_state.is_updating_data = False
                         # 使用safer_rerun代替直接rerun
                         st.rerun()
        
        with col2:
            if st.button("生成PDF报告", key="generate_pdf_button"):
                try:
                    # --- Prerequisites Check ---
                    if 'raw_data' not in st.session_state or st.session_state.raw_data.empty:
                        st.warning("没有可用数据，无法生成PDF报告。请先查询数据。")
                        st.stop()

                    # Check required data columns for PDF content generation
                    required_data_cols = ['区县', '站名', '总降雨量', '近一小时雨量'] 
                    required_data_cols.extend(['乡镇', '区域内站点'])
                    missing_data_cols = [col for col in required_data_cols if col not in st.session_state.raw_data.columns]
                    if missing_data_cols:
                        st.error(f"无法生成完整的PDF，数据缺少以下必需列：{', '.join(missing_data_cols)}")

                    # Check required map columns
                    required_map_cols = ['经度', '纬度', '总降雨量', '区县', '站名', '乡镇', '区域内站点']
                    missing_map_cols = [col for col in required_map_cols if col not in st.session_state.raw_data.columns]

                    # --- Generate Map Image Data (if possible) ---
                    map_buffer = None 
                    # Check if map generation is feasible (required columns exist)
                    if not missing_map_cols:
                        # --- UPDATED: Prioritize Cache, Regenerate if Missing/Invalid --- START
                        cached_map = st.session_state.get('map_image_buffer')

                        map_buffer = None  # 初始化map_buffer
                        
                        if isinstance(cached_map, io.BytesIO):
                            # Cache hit and valid type - 创建副本以避免指针问题
                            st.info("检测到已缓存的地图图像，将直接使用。")
                            try:
                                cached_map.seek(0)  # 重置原始缓存的指针
                                map_data = cached_map.read()  # 读取数据
                                map_buffer = io.BytesIO(map_data)  # 创建新的BytesIO对象
                                map_buffer.seek(0)  # 确保指针在开始位置
                                print(f"PDF Gen: Using cached map image, size: {len(map_data)} bytes")
                                st.success(f"✅ 使用缓存地图（大小：{len(map_data)/1024:.1f} KB）")
                            except Exception as cache_error:
                                st.warning(f"读取缓存地图时出错：{cache_error}，将重新生成地图")
                                print(f"PDF Gen: Error reading cached map: {cache_error}")
                                map_buffer = None  # 缓存读取失败，需要重新生成
                        
                        if map_buffer is None:
                            # Cache miss or invalid type - Attempt to generate
                            st.info("未找到有效地图缓存，正在尝试生成...")
                            try:
                                with st.spinner("正在准备地图数据..."): # Add spinner here
                                    current_map_data_for_pdf = st.session_state.raw_data.copy()
                                    
                                    # 调试信息：显示原始数据量
                                    print(f"PDF Gen: 原始数据行数: {len(current_map_data_for_pdf)}")
                                    st.info(f"📊 原始数据：{len(current_map_data_for_pdf)} 行")
                                    
                                    # 数值化处理
                                    numeric_cols_map = ['经度', '纬度', '总降雨量']
                                    for col in numeric_cols_map:
                                        if col in current_map_data_for_pdf.columns:
                                            current_map_data_for_pdf[col] = pd.to_numeric(current_map_data_for_pdf[col], errors='coerce')
                                    
                                    # 更宽松的过滤：只要有经纬度就可以显示，总降雨量可以为0或缺失
                                    before_filter = len(current_map_data_for_pdf)
                                    current_map_data_for_pdf = current_map_data_for_pdf.dropna(subset=['经度', '纬度'])
                                    after_coord_filter = len(current_map_data_for_pdf)
                                    
                                    # 对于总降雨量缺失的，设为0（这样可以在地图上显示为无降雨）
                                    if '总降雨量' in current_map_data_for_pdf.columns:
                                        current_map_data_for_pdf['总降雨量'] = current_map_data_for_pdf['总降雨量'].fillna(0)
                                    
                                    print(f"PDF Gen: 坐标过滤后数据行数: {after_coord_filter} (原始: {before_filter})")
                                    st.info(f"🗺️ 地图数据：{after_coord_filter} 个站点有坐标信息")

                                    if not current_map_data_for_pdf.empty:
                                        print("PDF Gen: Attempting to generate map image (cache miss)...")
                                        temp_map_buffer = plotting.generate_rainfall_map(current_map_data_for_pdf)
                                        if isinstance(temp_map_buffer, io.BytesIO):
                                            # 创建副本用于PDF，避免指针冲突
                                            temp_map_buffer.seek(0)
                                            map_data = temp_map_buffer.read()
                                            
                                            # 存储到session state（重置指针）
                                            st.session_state.map_image_buffer = io.BytesIO(map_data)
                                            st.session_state.map_image_buffer.seek(0)
                                            
                                            # 为PDF创建独立副本
                                            map_buffer = io.BytesIO(map_data)
                                            map_buffer.seek(0)
                                            
                                            st.success(f"✅ 地图图像已生成（大小：{len(map_data)/1024:.1f} KB）")
                                            print(f"PDF Gen: Map image generated and stored, size: {len(map_data)} bytes")
                                        else:
                                            st.warning("地图生成函数未能返回有效的图像数据。可能原因：缺少地图边界文件（地图文件夹）或DEM高程数据。PDF将不包含地图。")
                                            st.info("💡 提示：如需完整地图显示，请确保项目目录下存在'地图'文件夹，包含必要的.shp边界文件。")
                                            st.session_state.map_image_buffer = None # Ensure state is cleared
                                            print("PDF Gen: Map generation failed or returned invalid type (cache miss).")
                                    else:
                                        st.warning("用于地图的数据为空，PDF将不包含地图。")
                                        st.session_state.map_image_buffer = None # Ensure state is cleared
                                        print("PDF Gen: No data for map generation after filtering (cache miss).")
                            except Exception as map_gen_error:
                                 st.error(f"在为PDF准备地图时出错: {map_gen_error}")
                                 print(f"PDF Gen: Error during map generation (cache miss): {map_gen_error}")
                                 st.session_state.map_image_buffer = None # Clear state on error
                        # --- UPDATED: Prioritize Cache, Regenerate if Missing/Invalid --- END
                    else:
                        st.warning(f"无法生成地图（缺少列: {', '.join(missing_map_cols)}），PDF将不包含地图。")
                        st.session_state.map_image_buffer = None # Clear cache if columns missing
                        print(f"PDF Gen: Cannot generate map due to missing columns: {missing_map_cols}")

                    # --- Prepare Data for PDF Tables ---
                    with st.spinner("正在准备报告数据..."):
                        # 1. 复制原始数据
                        data_for_pdf = st.session_state.raw_data.copy()
                        
                        # 2. 只包括用户选择要使用的数据
                        data_for_pdf = data_for_pdf[data_for_pdf['use_data']].copy()
                        print(f"PDF Gen: {len(data_for_pdf)} rows selected based on 'use_data' flag.")

                        # 3. 转换数值列
                        numeric_cols_pdf = ['总降雨量', '近一小时雨量']
                        for col in numeric_cols_pdf:
                            if col in data_for_pdf.columns:
                                data_for_pdf[col] = pd.to_numeric(data_for_pdf[col], errors='coerce')
                        
                        # 4. 关键逻辑：将勾选使用但没有数据的站点的总雨量设置为0
                        if '总降雨量' in data_for_pdf.columns:
                            data_for_pdf['总降雨量'] = data_for_pdf['总降雨量'].fillna(0)
                            print("PDF Gen: Filled NaN in '总降雨量' with 0 for PDF report.")
                        
                        st.success("✅ 报告数据准备完成")

                    # --- Generate PDF --- #
                    with st.spinner("正在生成PDF报告..."):
                        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                        pdf_filename = f"./pdf/雨情报告_{timestamp}.pdf"

                        # 确保pdf目录存在
                        os.makedirs(os.path.dirname(pdf_filename), exist_ok=True)

                        # Ensure temp file doesn't exist from previous failed run
                        if os.path.exists(pdf_filename):
                            try:
                                os.remove(pdf_filename)
                            except OSError as e:
                                st.error(f"无法删除旧的临时PDF文件 '{pdf_filename}': {e}. 请手动删除。")
                                st.stop()

                        # Retrieve query times
                        start_dt = st.session_state.query_params.get('start_datetime', datetime.datetime.now())
                        end_dt = st.session_state.query_params.get('end_datetime', datetime.datetime.now())

                        # 使用新API获取最大小时雨量数据用于PDF
                        try:
                            pdf_max_hourly_result = asyncio.run(get_pinggu_max_hourly_rain(start_dt, end_dt))
                            pdf_max_intensity_data = None
                            if pdf_max_hourly_result is not None:
                                max_rainfall, station_name, max_time = pdf_max_hourly_result
                                # 解析时间获取小时:分钟格式
                                end_time_display = max_time[11:16] if len(max_time) >= 16 else '--:--'
                                
                                # 计算开始时间（小时雨量是前一个小时到当前时间的降水量）
                                try:
                                    if end_time_display != '--:--':
                                        end_hour = int(end_time_display[:2])
                                        start_hour = end_hour - 1 if end_hour > 0 else 23
                                        start_time_display = f"{start_hour:02d}:00"
                                    else:
                                        start_time_display = '--:--'
                                except:
                                    start_time_display = '--:--'
                                    
                                pdf_max_intensity_data = {
                                    'station_name': station_name,
                                    'start_time': start_time_display,
                                    'end_time': end_time_display,
                                    'intensity': max_rainfall
                                }
                        except Exception as e:
                            logger.error(f"PDF生成时调用新API失败: {e}")
                            # 如果新API失败，回退到旧方法
                            pdf_max_hourly_data = get_max_hourly_data(None)
                            pdf_max_intensity_data = None
                            if pdf_max_hourly_data['success']:
                                # 处理备用方法的时间显示
                                backup_end_time = pdf_max_hourly_data['time'][:5] if pdf_max_hourly_data['time'] else '--:--'
                                backup_start_time = '--:--'
                                
                                try:
                                    if backup_end_time != '--:--' and len(backup_end_time) >= 5:
                                        end_hour = int(backup_end_time[:2])
                                        start_hour = end_hour - 1 if end_hour > 0 else 23
                                        backup_start_time = f"{start_hour:02d}:00"
                                except:
                                    backup_start_time = '--:--'
                                
                                pdf_max_intensity_data = {
                                    'station_name': pdf_max_hourly_data['station_name'],
                                    'start_time': backup_start_time,
                                    'end_time': backup_end_time,
                                    'intensity': pdf_max_hourly_data['max_rainfall']
                                }

                        # 最终地图状态检查
                        if map_buffer is not None:
                            try:
                                map_buffer.seek(0)
                                map_size = len(map_buffer.read())
                                map_buffer.seek(0)  # 重置指针供PDF使用
                                st.info(f"🗺️ 将包含地图到PDF中（大小：{map_size/1024:.1f} KB）")
                                print(f"PDF Gen: Final map check - map available, size: {map_size} bytes")
                            except Exception as e:
                                st.warning(f"地图数据检查失败：{e}")
                                print(f"PDF Gen: Final map check failed: {e}")
                                map_buffer = None
                        else:
                            st.warning("⚠️ PDF将不包含地图")
                            print("PDF Gen: Final map check - no map available")

                        # Call the updated generate_pdf function
                        generate_pdf(
                            data=data_for_pdf, 
                            query_start_time=start_dt, 
                            query_end_time=end_dt, 
                            map_image_buffer=map_buffer,
                            output_filename=pdf_filename,
                            max_intensity_data=pdf_max_intensity_data
                        )

                        # --- Store PDF info in session state for later use --- #
                        if os.path.exists(pdf_filename):
                            st.session_state.pdf_filename = pdf_filename
                            st.session_state.pdf_generated = True
                            st.success(f"PDF报告已生成。现在您可以预览、下载或发布该报告。")
                            st.rerun()  # 重新运行以显示新的按钮
                        else:
                            st.error("PDF文件生成失败或未找到。请检查日志。")
                            
                except Exception as e:
                    st.error(f"处理PDF生成请求时发生意外错误: {e}")
                    import traceback
                    print("PDF生成按钮顶级错误:")
                    print(traceback.format_exc())

        # 添加PDF操作按钮行
        if 'pdf_generated' in st.session_state and st.session_state.pdf_generated and 'pdf_filename' in st.session_state:
            pdf_col1, pdf_col2, pdf_col3 = st.columns(3)
            
            with pdf_col1:
                if st.button("预览报告", key="preview_pdf_button"):
                    if os.path.exists(st.session_state.pdf_filename):
                        # 重置预览状态
                        st.session_state.show_preview = True
                        st.session_state.preview_error = None
                        st.session_state.pdf_content = None # 先清空旧内容
                        st.session_state.pdf_size = 0
                        if st.session_state.get('simple_pdf_mode'):
                            del st.session_state['simple_pdf_mode'] # 退出简化模式
                            
                        # 检查文件大小 (保留原始检查逻辑)
                        file_size = os.path.getsize(st.session_state.pdf_filename)
                        file_size_mb = file_size / 1024 / 1024
                        
                        # --- 修改开始：总是尝试加载前两页进行预览 ---
                        try:
                            with open(st.session_state.pdf_filename, "rb") as infile:
                                reader = PyPDF2.PdfReader(infile)
                                writer = PyPDF2.PdfWriter()

                                # 添加前两页（如果存在）
                                num_pages_to_preview = min(len(reader.pages), 2)
                                if num_pages_to_preview == 0:
                                     st.warning("PDF文件为空，无法预览。")
                                     st.session_state.show_preview = False # 不要进入预览模式
                                     st.rerun()

                                for i in range(num_pages_to_preview):
                                    writer.add_page(reader.pages[i])

                                # 将选定的页面写入内存缓冲区
                                preview_buffer = io.BytesIO()
                                writer.write(preview_buffer)
                                preview_buffer.seek(0) # 重置缓冲区指针
                                pdf_data = preview_buffer.read()

                            st.session_state.pdf_content = base64.b64encode(pdf_data).decode('utf-8')
                            st.session_state.pdf_size = len(pdf_data) # 更新为预览内容的大小
                            print(f"PDF预览加载成功 (前 {num_pages_to_preview} 页): {st.session_state.pdf_filename}, 预览大小: {len(pdf_data)} 字节")
                            st.info(f"当前仅预览报告的前 {num_pages_to_preview} 页。") # 提示用户

                        except FileNotFoundError:
                            st.error(f"找不到PDF文件: {st.session_state.pdf_filename}")
                            st.session_state.preview_error = "文件未找到"
                            print(f"PDF文件不存在: {st.session_state.pdf_filename}")
                        except PyPDF2.errors.PdfReadError as e:
                            st.error(f"读取PDF文件时出错: {e}。文件可能已损坏。")
                            st.session_state.preview_error = f"PDF读取错误: {e}"
                            print(f"PDF读取错误: {e}")
                        except Exception as e:
                            st.error(f"加载PDF预览时发生未知错误: {e}")
                            st.session_state.preview_error = str(e)
                            print(f"PDF加载错误 (预览): {e}")
                            
                        # 移除原来基于大小判断加载完整PDF的逻辑
                        # if file_size_mb > 5: ... else: ... end

                        # 使用rerun确保PDF在按钮行之外显示
                        st.rerun()
                        # --- 修改结束 ---
                    else:
                        st.error(f"找不到PDF文件: {st.session_state.pdf_filename}")
                        print(f"PDF文件不存在: {st.session_state.pdf_filename}")
            
            with pdf_col2:
                try:
                    with open(st.session_state.pdf_filename, "rb") as pdf_file:
                        pdf_bytes = pdf_file.read()
                    
                    st.download_button(
                        label="下载报告",
                        data=pdf_bytes,
                        file_name=st.session_state.pdf_filename,
                        mime="application/pdf",
                        key="download_pdf"
                    )
                except Exception as e:
                    st.error(f"准备下载按钮时发生错误: {e}")
            
            with pdf_col3:
                if st.button("自动发布", key="auto_publish_button"):
                    try:
                        # 确保源文件存在
                        if not os.path.exists(st.session_state.pdf_filename):
                            st.error(f"源文件不存在：{st.session_state.pdf_filename}")
                            st.stop()
                            
                        # 创建目标目录（如果不存在）
                        target_dir = r"D:\共享\预警信号"
                        os.makedirs(target_dir, exist_ok=True)
                        
                        # 复制文件到目标目录
                        target_file = os.path.join(target_dir, os.path.basename(st.session_state.pdf_filename))
                        shutil.copy2(st.session_state.pdf_filename, target_file)
                        
                        # 创建JSON数据
                        json_data = {
                            "path": os.path.basename(st.session_state.pdf_filename),
                            "method": "图片",
                            "object":  ['文件传输助手', '气象信息', '付强', '平谷区地灾防治', '平谷区森防办', '平谷蔬菜产业发展群', '平谷防汛信息群', '（平蓟三兴）汛期联动群', '应急与防汛值班', '区城市应急及防汛工作群', '北三河区域气象联防群', '平谷应急灾情管理工作群', '04-农业服务群', '平谷消防气象信息共享群', '农技员工作群（镇罗营）', '京津承联防群', '中铁投资承平高速保险服务群', '首发承平京平四标保险服务群', '首发承平京平三标保险服务群', '首发承平京平一标保险服务群', '茅山后村气象服务', '平谷区气象服务群', '2025年清明祭扫指挥部联络群']
                        }
                        
                        # 保存JSON文件
                        json_file_path = r"D:\共享\wx.json"
                        with open(json_file_path, 'w', encoding='utf-8') as f:
                            json.dump(json_data, f, ensure_ascii=False, indent=4)
                            
                        st.success(f"文件已复制到 {target_dir} 并已生成 wx.json 文件")

                    except Exception as e:
                        st.error(f"自动发布过程中出错: {e}")
                        print(f"自动发布错误: {e}")
                
            # 在所有列布局之外显示PDF预览，这样可以占据整个页面宽度
            if 'show_preview' in st.session_state and st.session_state.show_preview:
                # 创建一个清晰的分隔
                st.write("")
                st.write("---")
                st.markdown("### PDF报告预览")
                
                # 添加更多调试信息
                preview_debug_expander = st.expander("🔍 预览调试信息")
                with preview_debug_expander:
                    st.write(f"当前预览文件名: {st.session_state.get('pdf_filename', '未设置')}")
                    content_exists = 'pdf_content' in st.session_state and st.session_state.pdf_content is not None
                    st.write(f"PDF内容已加载: {content_exists}")
                    if content_exists:
                        st.write(f"Base64内容长度: {len(st.session_state.pdf_content)}")
                    if 'pdf_size' in st.session_state:
                         st.write(f"文件大小 (字节): {st.session_state.pdf_size}")
                    if st.session_state.get('preview_error'):
                        st.write(f"预览加载错误: {st.session_state.preview_error}")
                
                if st.session_state.get('preview_error'):
                    st.error(f"预览加载时出错: {st.session_state.preview_error}")
                elif 'pdf_content' in st.session_state and st.session_state.pdf_content:
                    # 使用简化的iframe显示PDF
                    pdf_container = st.container()
                    with pdf_container:
                        try:
                            pdf_display = f"""
                            <iframe 
                                src="data:application/pdf;base64,{st.session_state.pdf_content}#view=FitH" 
                                width="100%" 
                                height="1000px" 
                                style="border: 1px solid #ddd; border-radius: 5px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                                您的浏览器不支持内嵌PDFs。
                            </iframe>
                            """
                            st.markdown(pdf_display, unsafe_allow_html=True)
                            st.info("如果内容显示不全或过小，请尝试使用浏览器缩放功能 (Ctrl + 滚轮)。")
                        except Exception as e:
                            st.error(f"预览渲染错误: {e}")
                            print(f"PDF预览渲染错误: {e}")
                else:
                    st.warning("未能加载PDF内容进行预览。请尝试重新生成报告或使用下载按钮。")
                
                # 添加关闭预览的按钮
                if st.button("关闭预览", key="close_preview"):
                    st.session_state.show_preview = False
                    if 'pdf_content' in st.session_state:
                        del st.session_state.pdf_content
                    if 'pdf_size' in st.session_state:
                        del st.session_state.pdf_size
                    if 'preview_error' in st.session_state:
                        del st.session_state.preview_error
                    # 如果处于简化模式，需要重置，以便下次预览完整的
                    if st.session_state.get('simple_pdf_mode'):
                        del st.session_state['simple_pdf_mode']
                        # 可能需要重新查询或生成原始PDF文件名
                        # 这里假设用户会重新生成或查询
                        st.info("已退出简化预览模式。")
                    st.rerun()

        with tab2:
            st.markdown('<h2 class="table-title">降水地图分布</h2>', unsafe_allow_html=True)

            # map_output_filename = '年降雨量分布图_clipped.png' # Define expected output filename # REMOVED

            # Check if raw_data exists and has necessary columns
            if 'raw_data' in st.session_state and not st.session_state.raw_data.empty:
                # st.write("--- Debug (Map Tab Start) ---") # Debug Start Map Tab REMOVED
                # st.write("raw_data head:", st.session_state.raw_data[['站名', '经度', '纬度', '总降雨量']].head()) # Debug raw_data REMOVED
                required_cols = ['经度', '纬度', '总降雨量', '区县', '站名', '乡镇', '区域内站点'] # Columns needed by draw.py
                missing_cols = [col for col in required_cols if col not in st.session_state.raw_data.columns]

                if not missing_cols:
                    try:
                        # --- Call the drawing function ---
                        # Pass the potentially updated raw_data
                        current_map_data = st.session_state.raw_data.copy()
                        # Ensure required columns are numeric where expected by generate_rainfall_map
                        numeric_cols = ['经度', '纬度', '总降雨量']
                        for col in numeric_cols:
                                if col in current_map_data.columns:
                                    current_map_data[col] = pd.to_numeric(current_map_data[col], errors='coerce')
                        
                        # Filter out rows with NaN in essential coordinates or values before plotting
                        current_map_data.dropna(subset=['经度', '纬度', '总降雨量'], inplace=True)

                        if not current_map_data.empty:
                                with st.spinner("正在生成降水分布图..."):
                                    # 调用修改后的函数，接收返回的图片数据

                                    # --- DEBUG: Check data passed to plotting function --- START
                                  
                                    buffer = io.StringIO()
                                    current_map_data.info(buf=buffer)
                                    s = buffer.getvalue()
                                   
                                    # --- DEBUG: Check data passed to plotting function --- END

                                    image_buffer = plotting.generate_rainfall_map(current_map_data) # Pass the DataFrame
                                    # 保存生成的图像到session_state中，供后续下载PDF使用
                                    # 检查返回的是否是 BytesIO 对象
                                    if isinstance(image_buffer, io.BytesIO):
                                        st.session_state.map_image_buffer = image_buffer
                                    elif image_buffer: # 如果不是 BytesIO 但返回了 True-like 值
                                        st.warning("地图函数返回了非预期的类型，可能无法在PDF中使用。")
                                        st.session_state.map_image_buffer = None # 设为None避免后续错误
                                    else:
                                        st.session_state.map_image_buffer = None

                                    # st.write(f"generate_rainfall_map returned type: {type(image_buffer)}") # Debug return type REMOVED

                                # --- Display the generated map ---
                                # 检查 session state 中的 buffer 是否有效
                                map_buffer_to_display = st.session_state.get('map_image_buffer')
                                if map_buffer_to_display:
                                    # 确保指针在开头
                                    map_buffer_to_display.seek(0)
                                    # 直接使用 st.image 显示内存中的图片数据
                                    st.image(map_buffer_to_display, caption='平谷及周边降水量分布图', use_column_width=True)
                                else:
                                    # 如果函数返回 None 或 False，说明生成失败
                                    st.error("地图数据生成失败或未存储，无法显示。请检查 `plotting.py` 的日志输出或逻辑。")
                                    # st.write("Debug: image_buffer was None or False") # Debug failure case REMOVED

                        else:
                                st.warning("数据过滤后为空，无法生成地图。请检查原始数据中的经纬度和降雨量值。")

                      
                    except ImportError as e:
                            st.error(f"绘图时发生错误：缺少必要的库。请确保已安装 `cartopy` 及其依赖。错误详情: {e}")
                    except Exception as e:
                            st.error(f"生成地图时发生未知错误: {e}")
                            # Optionally print traceback for debugging in console
                            import traceback
                            print("Error during map generation in Streamlit:")
                            print(traceback.format_exc())

                else:
                    st.warning(f"无法生成地图，`st.session_state.raw_data` 中缺少必需的列: {', '.join(missing_cols)}。请确保 API 返回或数据处理中包含这些列。")
            else:
                st.info("请先查询数据以生成降水分布图。")


        # st.markdown('</div>', unsafe_allow_html=True) # 移除这个多余的闭合标签
        
      
else:
    # Show initial instructions with improved styling
    st.markdown("""
    <div class="info-box" style="margin: 30px 0;">
        <h3 style="color: #3498db; margin-top: 0;">使用说明</h3>
        <p style="margin-bottom: 0;">请在左侧设置查询条件并点击查询按钮来获取降水数据。您可以：</p>
        <ul style="margin-top: 10px;">
            <li>选择特定的日期和时间范围</li>
            <li>查看表格和地图形式的降水数据</li>
            <li>直接编辑表格中的数据，系统会自动更新图表</li>
            <li>分析降水趋势和分布情况</li>
            <li>导出数据为PDF格式</li>
        </ul>
    </div>
    """, unsafe_allow_html=True)
    
    # Add sample visualization
    st.markdown('<h3 style="color: #3498db; text-align: center; margin: 30px 0;">系统功能预览</h3>', unsafe_allow_html=True)
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        <div style="background-color: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); height: 100%;">
            <h4 style="color: #3498db; text-align: center;">📊 数据可视化</h4>
            <p style="text-align: center;">系统提供多种图表展示降水数据趋势和分布</p>
            <div style="text-align: center; margin-top: 20px;">
                <img src="https://img.icons8.com/color/96/000000/combo-chart--v1.png" style="width: 80px; height: 80px;">
            </div>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("""
        <div style="background-color: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); height: 100%;">
            <h4 style="color: #3498db; text-align: center;">🗺️ 地理分析</h4>
            <p style="text-align: center;">通过地图直观展示各地区降水分布情况</p>
            <div style="text-align: center; margin-top: 20px;">
                <img src="https://img.icons8.com/color/96/000000/map-marker.png" style="width: 80px; height: 80px;">
            </div>
        </div>
        """, unsafe_allow_html=True)
    
    # Add editable data demo
    st.markdown('<h3 style="color: #3498db; text-align: center; margin: 30px 0;">数据编辑功能</h3>', unsafe_allow_html=True)
    
    st.markdown("""
    <div style="background-color: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <p style="text-align: center;">本系统支持直接编辑表格数据，修改后的数据会自动更新到所有图表中</p>
        <div style="text-align: center; margin-top: 20px;">
            <img src="https://img.icons8.com/color/96/000000/edit.png" style="width: 80px; height: 80px;">
        </div>
    </div>
    """, unsafe_allow_html=True)

# Footer with improved styling
st.markdown('<div class="footer">', unsafe_allow_html=True)
col1, col2 = st.columns(2)
with col1:
    st.markdown(f"<p>数据更新时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>", unsafe_allow_html=True)
with col2:
    st.markdown("<p style='text-align: right;'>降水数据统计系统 v1.1</p>", unsafe_allow_html=True)
st.markdown('</div>', unsafe_allow_html=True)