#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
站点信息API - 主程序入口
整合站点基础信息和降雨数据
"""

import os
import asyncio
from datetime import datetime, timedelta

from .utils import setup_logger, load_config
from .data import StationData
from .api import MaxRainAPI

# 获取日志对象
logger = setup_logger('main')

async def main(start_time=None, end_time=None, station_type='全部', csv_path='全部站点信息.csv'):
    """
    主函数
    
    Args:
        start_time: 开始时间，默认为当前时间前24小时
        end_time: 结束时间，默认为当前时间
        station_type: 站点类型，可选值：'全部'或'称重站'
        csv_path: CSV文件路径
    
    Returns:
        站点数据DataFrame
    """
    # 设置默认的时间范围
    if end_time is None:
        end_time = datetime.now()
    if start_time is None:
        start_time = end_time - timedelta(days=1)
    
    # 加载配置
    config_path = os.path.join(os.path.dirname(__file__), 'config', 'config.yaml')
    config = load_config(config_path)
    
    if not config:
        logger.error("配置加载失败")
        return None
    
    # 创建站点数据处理器
    station_data = StationData(config, csv_path)
    
    try:
        # 获取雨量数据
        logger.info(f"获取雨量数据: 时间范围 {start_time} 至 {end_time}, 站点类型 {station_type}")
        result = await station_data.get_all_rain_data(start_time, end_time, station_type)
        
        if result is not None and not result.empty:
            logger.info(f"成功获取雨量数据，共 {len(result)} 个站点")
        else:
            logger.warning("未获取到有效的雨量数据")
        
        return result
    except Exception as e:
        logger.error(f"获取雨量数据时发生错误: {e}")
        return None
    finally:
        # 关闭客户端
        await station_data.close()

async def get_pinggu_max_hourly_rain(start_time=None, end_time=None, station_type='全部'):
    """
    查询平谷区所有站点中的最大小时雨量
    
    Args:
        start_time: 开始时间，默认为当前时间前24小时
        end_time: 结束时间，默认为当前时间
        station_type: 站点类型，可选值：'全部'或'称重站'
    
    Returns:
        tuple: (最大雨量值, 站点名称, 最大值时间) 或 None（如果查询失败）
    """
    # 设置默认的时间范围
    if end_time is None:
        end_time = datetime.now()
    if start_time is None:
        start_time = end_time - timedelta(days=1)
    
    # 加载配置
    config_path = os.path.join(os.path.dirname(__file__), 'config', 'config.yaml')
    config = load_config(config_path)
    
    if not config:
        logger.error("配置加载失败")
        return None
    
    # 创建最大雨量API客户端
    max_rain_api = MaxRainAPI(config)
    
    try:
        logger.info(f"查询平谷区最大小时雨量: 时间范围 {start_time} 至 {end_time}, 站点类型 {station_type}")
        result = await max_rain_api.get_pinggu_max_hourly_rain(start_time, end_time, station_type)
        
        if result is not None:
            max_rain, station_name, max_time = result
            logger.info(f"查询成功：最大雨量 {max_rain}mm，站点 {station_name}，时间 {max_time}")
        else:
            logger.warning("查询平谷区最大小时雨量失败")
        
        return result
    except Exception as e:
        logger.error(f"查询平谷区最大小时雨量时发生错误: {e}")
        return None
    finally:
        # 关闭客户端
        await max_rain_api.close()

async def example():
    """示例函数"""
    # 设置时间范围
    start_time = datetime.now() - timedelta(days=1)
    end_time = datetime.now()
    
    # 获取雨量数据
    stations = await main(
        start_time=start_time,
        end_time=end_time,
        station_type='全部'
    )
    
    if stations is not None and not stations.empty:
        # 打印结果
        print(f"站点总数: {len(stations)}")
        print(f"包含以下列: {stations.columns.tolist()}")
        
        # 打印前5行数据
        print("\n前5行数据:")
        print(stations.head())

if __name__ == "__main__":
    # 运行示例
    asyncio.run(example()) 