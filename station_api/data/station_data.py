#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
站点数据处理模块 - 负责站点信息的加载和处理
"""

import pandas as pd
import asyncio
from typing import Dict, List, Any, Union, Optional
from datetime import datetime, timedelta

from ..utils import setup_logger, format_time
from ..api import RainAPI

# 获取日志对象
logger = setup_logger('station_data')

class StationData:
    """站点数据处理类，负责加载和处理站点基础信息及降雨数据"""
    
    def __init__(self, config: Dict[str, Any], csv_path: str = '全部站点信息.csv'):
        """
        初始化站点数据处理器
        
        Args:
            config: 配置信息
            csv_path: CSV文件路径
        """
        self.csv_path = csv_path
        self.config = config
        self.mappings = config['mapping']
        self.rain_api = RainAPI(config)
        self.stations_df = None
        self.load_stations()
    
    def load_stations(self) -> None:
        """加载站点基础信息CSV文件"""
        try:
            # 读取CSV文件
            self.stations_df = pd.read_csv(self.csv_path, index_col='区站号',encoding='utf8')
            logger.info(f"加载站点信息成功，共 {len(self.stations_df)} 个站点")
            
            # 处理缺失值
            self.stations_df = self.stations_df.fillna('')
            
            # 确保必要的列都存在
            required_columns = ['站名', '区县', '经度', '纬度', '区域内站点', '乡镇']
            for col in required_columns:
                if col not in self.stations_df.columns:
                    # 如果不存在，则添加空列
                    self.stations_df[col] = ''
                    logger.warning(f"CSV文件中缺少 {col} 列，已添加空列")
        except Exception as e:
            logger.warning(f"加载站点信息失败: {e}")
            # 创建一个空的DataFrame作为默认值
            self.stations_df = pd.DataFrame(columns=['区站号', '站名', '区县', '经度', '纬度', '区域内站点', '乡镇'])
            self.stations_df.set_index('区站号', inplace=True)
    
    async def get_stations_with_rain(self, 
                                    start_time: Union[str, datetime], 
                                    end_time: Union[str, datetime],
                                    station_type: str = '全部') -> pd.DataFrame:
        """
        获取所有站点的信息，包括雨量数据
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            station_type: 站点类型，可选值：'全部'或'称重站'
            
        Returns:
            pd.DataFrame: 包含所有站点信息和雨量数据的DataFrame
        """
        # 获取支持的区域
        supported_regions = list(self.mappings['region_to_province'].keys())
        
        # 创建任务列表
        tasks = [self.rain_api.get_rain_data(start_time, end_time, region, station_type) 
                for region in supported_regions]
        
        # 并行执行所有请求
        results = await asyncio.gather(*tasks)
        
        # 合并结果
        all_rain_data = pd.DataFrame()
        
        for i, rain_df in enumerate(results):
            if rain_df is not None and not rain_df.empty and isinstance(rain_df, pd.DataFrame):
                # 添加区域信息
                rain_df['region'] = supported_regions[i] if i < len(supported_regions) else "未知"
                # 合并数据
                all_rain_data = pd.concat([all_rain_data, rain_df], ignore_index=True)
        
        # 检查是否成功获取了数据
        if all_rain_data.empty:
            logger.warning("未获取到任何降雨数据")
            return pd.DataFrame()
        
        # 设置索引
        try:
            # 首先检查stationId列是否存在
            if 'stationId' not in all_rain_data.columns:
                logger.warning("雨量数据中缺少stationId列")
                return pd.DataFrame()
                
            # 尝试设置索引，不使用errors参数
            all_rain_data.set_index('stationId', inplace=True)
            
            # 检查索引是否重复
            if all_rain_data.index.duplicated().any():
                logger.warning("stationId列存在重复值，将保留最后一个值")
                all_rain_data = all_rain_data[~all_rain_data.index.duplicated(keep='last')]
        except Exception as e:
            logger.warning(f"设置索引时出错: {e}")
            return pd.DataFrame()
        
        # 提取时间列
        time_columns = self._extract_time_columns(all_rain_data)
        
        # 处理problem列
        all_rain_data = self._process_problem_column(all_rain_data, time_columns)
        
        return all_rain_data
    
    def _extract_time_columns(self, df: pd.DataFrame) -> List[str]:
        """
        从DataFrame中提取时间列
        
        Args:
            df: 降雨数据DataFrame
            
        Returns:
            List[str]: 时间列名列表
        """
        time_columns = []
        for col in df.columns:
            try:
                # 尝试将列名转换为datetime
                datetime.strptime(str(col), '%Y-%m-%d %H:%M:%S')
                time_columns.append(col)
                # 对该列的所有值进行数值转换
                df[col] = pd.to_numeric(df[col], errors='coerce')
            except ValueError:
                # 不是时间格式的列名，跳过
                continue
        return time_columns
    
    def _process_problem_column(self, df: pd.DataFrame, time_columns: List[str]) -> pd.DataFrame:
        """
        处理问题数据列
        
        Args:
            df: 降雨数据DataFrame
            time_columns: 时间列名列表
            
        Returns:
            pd.DataFrame: 处理后的DataFrame
        """
        # 创建problem列，默认为False
        df['problem'] = False
        
        # 检查是否存在problem列并且有重复
        has_problem_column = 'problem' in df.columns
        has_duplicated_columns = df.columns.duplicated().any()
        
        # 如果存在原始problem列并且有重复列名，需要处理
        if has_problem_column and has_duplicated_columns:
            # 找到所有为'problem'的列索引
            problem_indices = [i for i, col in enumerate(df.columns) if col == 'problem']
            if len(problem_indices) > 1:
                # 保留最后一个problem列，重命名其他的
                for i in problem_indices[:-1]:
                    # 使用df.iloc处理列
                    old_col = df.iloc[:, i]
                    # 创建一个临时列名
                    temp_col_name = f'problem_original_{i}'
                    # 将原始列复制到临时列
                    df[temp_col_name] = old_col
                    
                    # 处理这个临时列中的内容
                    for idx in df.index:
                        problem_times = df.loc[idx, temp_col_name]
                        # 检查problem列是否有内容
                        if pd.notna(problem_times) and str(problem_times).strip():
                            # 分割problem列的内容
                            problem_time_list = str(problem_times).split(',')
                            # 将这些时间点的值设为空
                            for time_str in problem_time_list:
                                time_str = time_str.strip()  # 去除可能的空格
                                if time_str in df.columns:
                                    df.loc[idx, time_str] = pd.NA
                    
                    # 删除临时列
                    df.drop(columns=[temp_col_name], inplace=True)
        
        # 检查时间列中的空值
        for col in time_columns:
            if col in df.columns:
                # 将有空值的行的problem列设为True
                df.loc[df[col].isna(), 'problem'] = True
        
        return df
    
    async def get_all_rain_data(self, 
                              start_time: Union[str, datetime], 
                              end_time: Union[str, datetime],
                              station_type: str = '全部') -> pd.DataFrame:
        """
        获取所有站点的雨量数据，包括总降雨量和最近一小时降雨量
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            station_type: 站点类型，可选值：'全部'或'称重站'
            
        Returns:
            pd.DataFrame: 包含所有站点信息和雨量数据的DataFrame
        """
        # 获取总降雨量数据
        all_rain_data = await self.get_stations_with_rain(start_time, end_time, station_type)
        
        # 处理最近一小时降雨量
        if isinstance(end_time, str):
            end_time_dt = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
        else:
            end_time_dt = end_time
            
        if isinstance(start_time, str):
            start_time_dt = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
        else:
            start_time_dt = start_time
        
        # 决定是否需要单独获取最近一小时数据
        last_hour_start = end_time_dt - timedelta(hours=1)
        last_hour_rain_data = pd.DataFrame() # 初始化为空

        if (end_time_dt - start_time_dt) == timedelta(hours=1):
            # 如果时间范围恰好是1小时，直接使用总降雨量数据
            last_hour_rain_data = all_rain_data.copy()
        elif end_time_dt.minute == 0:
            # 如果结束时间是整点，模拟原始代码行为：
            # 使用整个时间段的sumPre作为最近一小时雨量的数据源
            last_hour_rain_data = all_rain_data.copy()
            # 确保 last_hour_rain_data 有 sumPre 列 (从 all_rain_data 复制而来)
            last_hour_rain_data['sumPre'] = all_rain_data[end_time.strftime('%Y-%m-%d %H:%M:%S')]
              
        else:
            # 否则需要单独获取最近一小时的数据
            last_hour_rain_data = await self.get_stations_with_rain(
                last_hour_start, end_time_dt, station_type
            )
            # 如果获取失败，确保 last_hour_rain_data 是空 DataFrame
            if last_hour_rain_data is None:
                last_hour_rain_data = pd.DataFrame()


        # 将数据整合到stations_df中
        result_df = self.stations_df.copy()

        # 添加默认列以确保它们存在
        if '总降雨量' not in result_df.columns:
            result_df['总降雨量'] = pd.NA
        if 'problem' not in result_df.columns:
            result_df['problem'] = False
        if '近一小时雨量' not in result_df.columns:
            result_df['近一小时雨量'] = pd.NA

        try:
            # 合并总降雨量数据 (使用 update 进行原地更新)
            if not all_rain_data.empty:
                if 'sumPre' in all_rain_data.columns:
                    update_data = all_rain_data[['sumPre']].rename(columns={'sumPre': '总降雨量'})
                    result_df.update(update_data)
                if 'problem' in all_rain_data.columns:
                    result_df.update(all_rain_data[['problem']])

            # 合并最近一小时雨量数据
            if not last_hour_rain_data.empty:
                if 'sumPre' in last_hour_rain_data.columns:
                     # 使用 last_hour_rain_data 的 'sumPre' 列来更新 '近一小时雨量'
                    update_data = last_hour_rain_data[['sumPre']].rename(columns={'sumPre': '近一小时雨量'})
                    result_df.update(update_data)

        except Exception as e:
            logger.error(f"合并雨量数据时发生错误: {e}")
            return result_df

        return result_df
    
    async def close(self):
        """关闭API客户端"""
        await self.rain_api.close() 