api:
  base_url: 'http://10.224.97.125/music-ws/api'
  interfaceId: 'getRainStaticByTimeRange'
  serviceNodeId: 'BEPK_XXZX_LDAD_1'
  dataFormat: 'json'
  userId: 'BEPK_XXZX_LDAD'
  pwd: 'QXJ_p@ssw0rd'
  timeout: 30
  retry_attempts: 3
  retry_delay: 2
  headers:
    Accept: 'application/json, text/plain, */*'
    Accept-Language: 'zh-CN,zh;q=0.9,zh-TW;q=0.8'
    Connection: 'keep-alive'
    Referer: 'http://10.224.97.125/ldadv5/'
    User-Agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36'

mapping:
  district_to_region:
    "平谷": "平谷区"
    "顺义": "顺义区" 
    "密云": "密云区"
    "怀柔": "怀柔区"
    "通州": "通州区"
    "蓟州": "蓟州区"
    "承德": "承德市"
  
  region_to_province:
    "平谷区": "bjs"
    "顺义区": "bjs"
    "密云区": "bjs"
    "怀柔区": "bjs"
    "通州区": "bjs"
    "蓟州区": "tjs"
    "承德市": "hbs"

logging:
  level: INFO
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  file: 'station_info.log' 