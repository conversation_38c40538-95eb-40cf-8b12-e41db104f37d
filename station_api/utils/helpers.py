#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
辅助函数模块，提供配置加载和时间处理等功能
"""

import os
import yaml
from typing import Dict, Any, Union
from datetime import datetime

def load_config(config_path: str = 'config/config.yaml') -> Dict[str, Any]:
    """
    加载配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        Dict: 配置字典
    """
    config = {}
    try:
        # 检查相对路径和绝对路径
        paths_to_try = [
            config_path,
            os.path.join(os.path.dirname(os.path.dirname(__file__)), config_path)
        ]
        
        for path in paths_to_try:
            if os.path.exists(path):
                with open(path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                break
    except Exception:
        pass
    
    return config

def format_time(time_obj: Union[str, datetime]) -> str:
    """
    格式化时间对象为标准字符串
    
    Args:
        time_obj: 时间对象或字符串
        
    Returns:
        str: 格式化后的时间字符串
    """
    if isinstance(time_obj, datetime):
        return time_obj.strftime('%Y-%m-%d %H:%M:%S')
    return time_obj

def is_number(value: Any) -> bool:
    """
    检查值是否可以转换为数字
    
    Args:
        value: 待检查的值
        
    Returns:
        bool: 是否可以转换为数字
    """
    try:
        float(value)
        return True
    except (ValueError, TypeError):
        return False

def create_directory_if_not_exists(path: str) -> None:
    """
    如果目录不存在，则创建
    
    Args:
        path: 目录路径
    """
    directory = os.path.dirname(path)
    if directory and not os.path.exists(directory):
        os.makedirs(directory) 