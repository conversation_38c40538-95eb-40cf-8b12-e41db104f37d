#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import logging
import yaml

def setup_logger(name='station_api', config_path=None):
    """配置并返回日志对象，简化日志输出"""
    
    # 默认配置
    log_config = {
        'level': 'INFO',
        'format': '%(asctime)s - %(levelname)s - %(message)s',
        'file': 'station_info.log'
    }
    
    # 从配置文件加载配置
    if config_path and os.path.exists(config_path):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                if 'logging' in config:
                    log_config.update(config['logging'])
        except Exception:
            pass
    
    # 创建logger
    logger = logging.getLogger(name)
    
    # 检查handler是否已存在，避免重复添加
    if not logger.handlers:
        # 获取日志级别
        level = getattr(logging, log_config.get('level', 'INFO'))
        logger.setLevel(level)
        
        # 设置格式
        formatter = logging.Formatter(log_config.get('format'))
        
        # 添加控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # 添加文件处理器
        if log_config.get('file'):
            file_handler = logging.FileHandler(log_config.get('file'))
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
    
    return logger 