#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
雨量API请求模块 - 处理与降雨数据相关的API请求
"""

import json
import asyncio
import aiohttp
import pandas as pd
from typing import Dict, Any, Optional, Union
from datetime import datetime
import time

from ..utils import setup_logger, format_time

# 获取日志对象
logger = setup_logger('rain_api')

class RainAPI:
    """雨量API客户端，负责与降雨数据API通信"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化雨量API客户端
        
        Args:
            config: API配置信息
        """
        self.config = config['api']
        self.mappings = config['mapping']
        self._session = None
        self.timeout = aiohttp.ClientTimeout(total=self.config.get('timeout', 30))
        self.retry_attempts = self.config.get('retry_attempts', 3)
        self.retry_delay = self.config.get('retry_delay', 2)
    
    async def get_session(self) -> aiohttp.ClientSession:
        """
        获取或创建HTTP会话
        
        Returns:
            aiohttp.ClientSession: HTTP会话对象
        """
        if self._session is None or self._session.closed:
            self._session = aiohttp.ClientSession(
                timeout=self.timeout,
                headers=self.config.get('headers', {})
            )
        return self._session
    
    async def close(self):
        """关闭HTTP会话"""
        if self._session and not self._session.closed:
            await self._session.close()
            self._session = None
    
    async def get_rain_data(self, 
                           start_time: Union[str, datetime], 
                           end_time: Union[str, datetime],
                           region: str,
                           station_type: str = '全部') -> Optional[pd.DataFrame]:
        """
        获取指定地区的降雨数据
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            region: 地区名称
            station_type: 站点类型，可选值：'全部'或'称重站'
            
        Returns:
            Optional[pd.DataFrame]: 处理后的站点数据或None（如果请求失败）
        """
        # 格式化时间
        start_time_str = format_time(start_time)
        end_time_str = format_time(end_time)
        
        # 获取省份代码
        province = self.mappings['region_to_province'].get(region)
        if not province:
            logger.warning(f"不支持的地区: {region}")
            return None
        
        # 验证站点类型
        if station_type not in ['全部', '称重站']:
            logger.warning(f"不支持的站点类型: {station_type}，使用默认值'全部'")
            station_type = '全部'
        
        # 构建请求参数
        params = {
            'interfaceId': self.config['interfaceId'],
            'serviceNodeId': self.config['serviceNodeId'],
            'dataFormat': self.config['dataFormat'],
            'userId': self.config['userId'],
            'pwd': self.config['pwd'],
            'stationType': station_type,
            'startTime': start_time_str,
            'endTime': end_time_str,
            'province': province,
            'cnty': region
        }
        
        # 获取会话
        session = await self.get_session()
        
        # 带重试的请求发送
        for attempt in range(self.retry_attempts):
            try:
                async with session.get(
                    self.config['base_url'],
                    params=params,
                    ssl=False,  # 在生产环境中应当启用SSL验证
                    timeout=self.timeout
                ) as response:
                    if response.status != 200:
                        logger.warning(f"API请求失败 ({region}): 状态码 {response.status}，第 {attempt+1} 次尝试")
                        if attempt < self.retry_attempts - 1:
                            await asyncio.sleep(self.retry_delay)
                            continue
                        return None
                    
                    # 获取响应文本
                    text = await response.text()
                    
                    try:
                        # 解析JSON数据
                        data = json.loads(text)
                        return self._process_rain_data(data)
                    except json.JSONDecodeError:
                        logger.warning(f"JSON解析失败 ({region})，第 {attempt+1} 次尝试")
                        if attempt < self.retry_attempts - 1:
                            await asyncio.sleep(self.retry_delay)
                            continue
                        return None
            except (aiohttp.ClientError, asyncio.TimeoutError) as e:
                logger.warning(f"请求错误 ({region}): {str(e)}，第 {attempt+1} 次尝试")
                if attempt < self.retry_attempts - 1:
                    await asyncio.sleep(self.retry_delay)
                    continue
                return None
        
        return None
    
    def _process_rain_data(self, data: Dict[str, Any]) -> Optional[pd.DataFrame]:
        """
        处理降雨数据
        
        Args:
            data: API返回的原始数据
            
        Returns:
            Optional[pd.DataFrame]: 处理后的站点数据或None（如果数据格式不正确）
        """
        try:
            # 检查是否有data字段
            if not isinstance(data, dict) or 'data' not in data:
                return None
            
            data_content = data['data']
            
            # 检查是否有rainStaticByTimeRangeExcelList字段
            if not isinstance(data_content, dict) or 'rainStaticByTimeRangeExcelList' not in data_content:
                return None
                
            # 获取站点列表
            stations = data_content['rainStaticByTimeRangeExcelList']
            if not stations or not isinstance(stations, list):
                return None
            
            # 处理preMap字段，将其展开为多个列
            processed_stations = []
            for station in stations:
                # 复制站点基础信息
                processed_station = station.copy()
                
                # 处理preMap字段
                if 'preMap' in station and isinstance(station['preMap'], dict):
                    # 将preMap中的每个时间点直接作为新列添加到站点数据中
                    for time_point, value in station['preMap'].items():
                        # 时间点作为列名，降雨量作为值
                        processed_station[time_point] = value if value not in ['无', '--'] else pd.NA
                    
                    # 删除原始preMap字段
                    del processed_station['preMap']
                
                processed_stations.append(processed_station)
            
            # 将处理后的站点列表转换为DataFrame
            stations_df = pd.DataFrame(processed_stations)
            
            # 确保必要字段存在
            if 'stationId' not in stations_df.columns:
                return None
        
            return stations_df
            
        except Exception as e:
            logger.warning(f"处理站点数据时出错: {str(e)}")
            return None 