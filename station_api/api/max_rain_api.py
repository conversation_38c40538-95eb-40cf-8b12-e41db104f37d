#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
最大雨量API模块 - 查询特定区域的最大小时雨量
"""

import pandas as pd
from typing import Tuple, Optional, Union
from datetime import datetime

from ..utils import setup_logger
from .rain_api import RainAPI

# 获取日志对象
logger = setup_logger('max_rain_api')

class MaxRainAPI:
    """最大雨量API客户端，负责查询区域最大小时雨量"""
    
    def __init__(self, config: dict):
        """
        初始化最大雨量API客户端
        
        Args:
            config: API配置信息
        """
        self.rain_api = RainAPI(config)
    
    async def get_pinggu_max_hourly_rain(self, 
                                        start_time: Union[str, datetime], 
                                        end_time: Union[str, datetime],
                                        station_type: str = '全部') -> Optional[Tuple[float, str, str]]:
        """
        查询平谷区所有站点中的最大小时雨量
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            station_type: 站点类型，可选值：'全部'或'称重站'
            
        Returns:
            Optional[Tuple[float, str, str]]: 
            成功时返回(最大雨量值, 站点名称, 最大值时间)的元组
            失败时返回None
        """
        try:
            # 获取平谷区雨量数据
            logger.info(f"开始查询平谷区最大小时雨量，时间范围: {start_time} 至 {end_time}")
            
            rain_data = await self.rain_api.get_rain_data(start_time, end_time, "平谷区", station_type)
            
            if rain_data is None or rain_data.empty:
                logger.warning("未获取到平谷区雨量数据")
                return None
            
            # 提取时间列（所有以时间格式命名的列）
            time_columns = []
            for col in rain_data.columns:
                col_str = str(col)
                try:
                    # 尝试解析时间格式
                    datetime.strptime(col_str, '%Y-%m-%d %H:%M:%S')
                    time_columns.append(col)
                except ValueError:
                    continue
            
            if not time_columns:
                logger.warning("未找到时间列数据")
                return None
            
            logger.info(f"找到 {len(time_columns)} 个时间点数据")
            
            # 查找所有时间点中的最大雨量值
            max_rain_value = None
            max_station_id = ""
            max_station_name = ""
            max_time = ""
            
            for time_col in time_columns:
                # 确保该列的数据是数值类型
                rain_data[time_col] = pd.to_numeric(rain_data[time_col], errors='coerce')
                
                # 找到该时间点的最大值
                col_max_idx = rain_data[time_col].idxmax()
                
                # 跳过NaN值
                if pd.isna(col_max_idx):
                    continue
                
                col_max_value = rain_data.loc[col_max_idx, time_col]
                
                # 跳过NaN值
                if pd.isna(col_max_value):
                    continue
                
                # 更新全局最大值（初始值为None时直接赋值，否则比较大小）
                if max_rain_value is None or col_max_value > max_rain_value:
                    max_rain_value = float(col_max_value)
                    max_station_id = str(col_max_idx)  # stationId作为索引
                    max_time = str(time_col)
                    
                    # 获取站点名称
                    if 'stationName' in rain_data.columns:
                        max_station_name = str(rain_data.loc[col_max_idx, 'stationName'])
                    else:
                        max_station_name = max_station_id  # 如果没有站点名称，使用ID
            
            if max_rain_value is None:
                logger.warning("未找到有效的雨量数据")
                return None
            
            logger.info(f"找到最大小时雨量: {max_rain_value}mm，站点: {max_station_name}（ID: {max_station_id}），时间: {max_time}")
            
            return (max_rain_value, max_station_name, max_time)
            
        except Exception as e:
            logger.error(f"查询平谷区最大小时雨量时发生错误: {e}")
            return None
    
    async def close(self):
        """关闭API客户端"""
        await self.rain_api.close() 